import React from 'react';
import { Link } from 'react-router-dom';
import { Search, Plus, BarChart3, Users } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import Layout from '../components/Layout/Layout';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const quickActions = [
    {
      title: 'Search Scenarios',
      description: 'Find existing test scenarios by feature or keyword',
      icon: Search,
      link: '/search',
      color: 'bg-blue-500',
    },
    {
      title: 'Generate Scenarios',
      description: 'Use AI to generate new test scenarios with OpenAI',
      icon: Plus,
      link: '/generate',
      color: 'bg-green-500',
    },
    {
      title: 'View Analytics',
      description: 'See statistics about your test scenarios',
      icon: BarChart3,
      link: '/analytics',
      color: 'bg-purple-500',
    },
  ];

  const roleSpecificContent = {
    DEVELOPER: {
      title: 'Developer Dashboard',
      subtitle: 'Manage and search test scenarios for your features',
      tips: [
        'Create comprehensive test scenarios for new features',
        'Use automation-focused scenarios for CI/CD pipelines',
        'Collaborate with testers on scenario validation',
      ],
    },
    TESTER: {
      title: 'Tester Dashboard',
      subtitle: 'Execute and manage test scenarios',
      tips: [
        'Review scenarios for manual vs automation suitability',
        'Add detailed test steps and expected results',
        'Track test execution and results',
      ],
    },
  };

  const content = roleSpecificContent[user?.role || 'DEVELOPER'];

  return (
    <Layout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{content.title}</h1>
              <p className="text-gray-600 mt-1">{content.subtitle}</p>
              <p className="text-sm text-gray-500 mt-2">Welcome back, {user?.email}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-gray-400" />
              <span className="text-sm text-gray-600">Role: {user?.role}</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              to={action.link}
              className="card p-6 hover:shadow-lg transition-shadow duration-200 group"
            >
              <div className="flex items-center space-x-4">
                <div className={`${action.color} p-3 rounded-lg group-hover:scale-110 transition-transform duration-200`}>
                  <action.icon className="h-6 w-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    {action.title}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Role-specific Tips */}
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Tips for {user?.role}s</h2>
          <ul className="space-y-2">
            {content.tips.map((tip, index) => (
              <li key={index} className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-gray-700">{tip}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Recent Activity Placeholder */}
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
          <div className="text-center py-8 text-gray-500">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No recent activity to display</p>
            <p className="text-sm mt-1">Start by searching or generating test scenarios</p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
