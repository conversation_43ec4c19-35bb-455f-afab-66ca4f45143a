import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Sparkles, Download, Save } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { scenarioAPI } from '../services/api';
import { GenerateRequest, TestScenario } from '../types';
import Layout from '../components/Layout/Layout';
import toast from 'react-hot-toast';

const Generate: React.FC = () => {
  const { user } = useAuth();
  const [generatedScenarios, setGeneratedScenarios] = useState<TestScenario[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<GenerateRequest>();

  const scenarioTypes = [
    'FUNCTIONAL',
    'INTEGRATION',
    'UNIT',
    'E2E',
    'PERFORMANCE',
    'SECURITY',
    'USABILITY',
  ];

  const onSubmit = async (data: GenerateRequest) => {
    setIsGenerating(true);
    try {
      const response = await scenarioAPI.generateScenarios(data);
      setGeneratedScenarios(response.scenarios);

      if (response.scenarios.length > 0) {
        toast.success(response.message || `Generated ${response.scenarios.length} scenarios successfully!`);
      } else {
        toast.warning('No scenarios were generated. Please try with different parameters.');
      }
    } catch (error: any) {
      const errorData = error.response?.data;
      const message = errorData?.error || 'Failed to generate scenarios';
      const details = errorData?.details;
      const fallback = errorData?.fallback;

      toast.error(message);
      if (details) {
        console.error('Generation error details:', details);
      }
      if (fallback) {
        toast.info(fallback, { duration: 6000 });
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const exportScenarios = () => {
    const dataStr = JSON.stringify(generatedScenarios, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `test-scenarios-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Generate Test Scenarios</h1>
          <p className="text-gray-600 mt-1">Use AI to generate comprehensive test scenarios with Deepseek</p>
        </div>

        {/* Generation Form */}
        <div className="card p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Feature Name *
                </label>
                <input
                  {...register('feature', { required: 'Feature name is required' })}
                  type="text"
                  placeholder="e.g., User Authentication, Shopping Cart, File Upload"
                  className="input-field"
                />
                {errors.feature && (
                  <p className="mt-1 text-sm text-red-600">{errors.feature.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Test Type *
                </label>
                <select
                  {...register('type', { required: 'Test type is required' })}
                  className="input-field"
                >
                  <option value="">Select test type</option>
                  {scenarioTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Testing Method *
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    {...register('isAutomation', { required: 'Testing method is required' })}
                    type="radio"
                    value="true"
                    className="mr-2"
                  />
                  <span>Automation Testing</span>
                  {user?.role === 'TESTER' && (
                    <span className="ml-2 text-sm text-gray-500">
                      (Scenarios suitable for automated testing tools)
                    </span>
                  )}
                </label>
                <label className="flex items-center">
                  <input
                    {...register('isAutomation', { required: 'Testing method is required' })}
                    type="radio"
                    value="false"
                    className="mr-2"
                  />
                  <span>Manual Testing</span>
                  {user?.role === 'TESTER' && (
                    <span className="ml-2 text-sm text-gray-500">
                      (Scenarios requiring human judgment and observation)
                    </span>
                  )}
                </label>
              </div>
              {errors.isAutomation && (
                <p className="mt-1 text-sm text-red-600">{errors.isAutomation.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Context (Optional)
              </label>
              <textarea
                {...register('additionalContext')}
                rows={4}
                placeholder="Provide any additional context, requirements, or specific scenarios you want to focus on..."
                className="input-field"
              />
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isGenerating}
                className="btn-primary flex items-center space-x-2"
              >
                <Sparkles className="h-5 w-5" />
                <span>{isGenerating ? 'Generating...' : 'Generate Scenarios'}</span>
              </button>
            </div>
          </form>
        </div>

        {/* Generated Scenarios */}
        {generatedScenarios.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                Generated Scenarios ({generatedScenarios.length})
              </h2>
              <button
                onClick={exportScenarios}
                className="btn-secondary flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Export JSON</span>
              </button>
            </div>

            {generatedScenarios.map((scenario, index) => (
              <div key={scenario.id} className="card p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {scenario.title}
                    </h3>
                    <p className="text-gray-600 mb-4">{scenario.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      scenario.type === 'FUNCTIONAL' ? 'bg-blue-100 text-blue-800' :
                      scenario.type === 'INTEGRATION' ? 'bg-green-100 text-green-800' :
                      scenario.type === 'E2E' ? 'bg-purple-100 text-purple-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {scenario.type}
                    </span>
                    {user?.role === 'TESTER' && (
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        scenario.isAutomation 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {scenario.isAutomation ? 'Automation' : 'Manual'}
                      </span>
                    )}
                  </div>
                </div>

                {scenario.steps && scenario.steps.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Test Steps:</h4>
                    <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                      {scenario.steps.map((step, stepIndex) => (
                        <li key={stepIndex}>{step}</li>
                      ))}
                    </ol>
                  </div>
                )}

                {scenario.expectedResult && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-1">Expected Result:</h4>
                    <p className="text-sm text-gray-600">{scenario.expectedResult}</p>
                  </div>
                )}

                {scenario.tags && scenario.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {scenario.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {isGenerating && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Generating scenarios...</h3>
            <p className="text-gray-600">This may take a few moments</p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Generate;
