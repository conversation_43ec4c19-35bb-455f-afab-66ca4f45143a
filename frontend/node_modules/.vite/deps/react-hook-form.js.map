{"version": 3, "sources": ["../../react-hook-form/src/utils/isCheckBoxInput.ts", "../../react-hook-form/src/utils/isDateObject.ts", "../../react-hook-form/src/utils/isNullOrUndefined.ts", "../../react-hook-form/src/utils/isObject.ts", "../../react-hook-form/src/logic/getEventValue.ts", "../../react-hook-form/src/logic/getNodeParentName.ts", "../../react-hook-form/src/logic/isNameInFieldArray.ts", "../../react-hook-form/src/utils/isPlainObject.ts", "../../react-hook-form/src/utils/isWeb.ts", "../../react-hook-form/src/utils/cloneObject.ts", "../../react-hook-form/src/utils/compact.ts", "../../react-hook-form/src/utils/isUndefined.ts", "../../react-hook-form/src/utils/get.ts", "../../react-hook-form/src/utils/isBoolean.ts", "../../react-hook-form/src/utils/isKey.ts", "../../react-hook-form/src/utils/stringToPath.ts", "../../react-hook-form/src/utils/set.ts", "../../react-hook-form/src/constants.ts", "../../react-hook-form/src/useFormContext.tsx", "../../react-hook-form/src/logic/getProxyFormState.ts", "../../react-hook-form/src/useIsomorphicLayoutEffect.ts", "../../react-hook-form/src/useFormState.ts", "../../react-hook-form/src/utils/isString.ts", "../../react-hook-form/src/logic/generateWatchOutput.ts", "../../react-hook-form/src/useWatch.ts", "../../react-hook-form/src/useController.ts", "../../react-hook-form/src/controller.tsx", "../../react-hook-form/src/utils/flatten.ts", "../../react-hook-form/src/form.tsx", "../../react-hook-form/src/logic/appendErrors.ts", "../../react-hook-form/src/utils/convertToArrayPayload.ts", "../../react-hook-form/src/utils/createSubject.ts", "../../react-hook-form/src/utils/isPrimitive.ts", "../../react-hook-form/src/utils/deepEqual.ts", "../../react-hook-form/src/utils/isEmptyObject.ts", "../../react-hook-form/src/utils/isFileInput.ts", "../../react-hook-form/src/utils/isFunction.ts", "../../react-hook-form/src/utils/isHTMLElement.ts", "../../react-hook-form/src/utils/isMultipleSelect.ts", "../../react-hook-form/src/utils/isRadioInput.ts", "../../react-hook-form/src/utils/isRadioOrCheckbox.ts", "../../react-hook-form/src/utils/live.ts", "../../react-hook-form/src/utils/unset.ts", "../../react-hook-form/src/utils/objectHasFunction.ts", "../../react-hook-form/src/logic/getDirtyFields.ts", "../../react-hook-form/src/logic/getCheckboxValue.ts", "../../react-hook-form/src/logic/getFieldValueAs.ts", "../../react-hook-form/src/logic/getRadioValue.ts", "../../react-hook-form/src/logic/getFieldValue.ts", "../../react-hook-form/src/logic/getResolverOptions.ts", "../../react-hook-form/src/utils/isRegex.ts", "../../react-hook-form/src/logic/getRuleValue.ts", "../../react-hook-form/src/logic/getValidationModes.ts", "../../react-hook-form/src/logic/hasPromiseValidation.ts", "../../react-hook-form/src/logic/hasValidation.ts", "../../react-hook-form/src/logic/isWatched.ts", "../../react-hook-form/src/logic/iterateFieldsByAction.ts", "../../react-hook-form/src/logic/schemaErrorLookup.ts", "../../react-hook-form/src/logic/shouldRenderFormState.ts", "../../react-hook-form/src/logic/shouldSubscribeByName.ts", "../../react-hook-form/src/logic/skipValidation.ts", "../../react-hook-form/src/logic/unsetEmptyArray.ts", "../../react-hook-form/src/logic/updateFieldArrayRootError.ts", "../../react-hook-form/src/utils/isMessage.ts", "../../react-hook-form/src/logic/getValidateError.ts", "../../react-hook-form/src/logic/getValueAndMessage.ts", "../../react-hook-form/src/logic/validateField.ts", "../../react-hook-form/src/logic/createFormControl.ts", "../../react-hook-form/src/logic/generateId.ts", "../../react-hook-form/src/logic/getFocusFieldName.ts", "../../react-hook-form/src/utils/append.ts", "../../react-hook-form/src/utils/fillEmptyArray.ts", "../../react-hook-form/src/utils/insert.ts", "../../react-hook-form/src/utils/move.ts", "../../react-hook-form/src/utils/prepend.ts", "../../react-hook-form/src/utils/remove.ts", "../../react-hook-form/src/utils/swap.ts", "../../react-hook-form/src/utils/update.ts", "../../react-hook-form/src/useFieldArray.ts", "../../react-hook-form/src/useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import * as React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport { FieldValues, UseFormStateProps, UseFormStateReturn } from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            ),\n          ),\n      }),\n    [name, control, disabled, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  UseFromSubscribe,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  const _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFromSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = values as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          set(fieldValues, name, undefined);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        for (const fieldName of _names.mount) {\n          setValue(\n            fieldName as FieldPath<TFieldValues>,\n            get(values, fieldName),\n          );\n        }\n      }\n\n      _formValues = cloneObject(values) as TFieldValues;\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  React.useEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport { FieldValues, FormState, UseFormProps, UseFormReturn } from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...(props.formControl ? props.formControl : createFormControl(props)),\n      formState,\n    };\n\n    if (\n      props.formControl &&\n      props.defaultValues &&\n      !isFunction(props.defaultValues)\n    ) {\n      props.formControl.reset(props.defaultValues, props.resetOptions);\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "mappings": ";;;;;;;;;;AAEA,IAAA,kBAAe,CAAC,YACd,QAAQ,SAAS;ACHnB,IAAA,eAAe,CAAC,UAAkC,iBAAiB;ACAnE,IAAA,oBAAe,CAAC,UAA8C,SAAS;ACGhE,IAAM,eAAe,CAAC,UAC3B,OAAO,UAAU;AAEnB,IAAA,WAAe,CAAmB,UAChC,CAAC,kBAAkB,KAAK,KACxB,CAAC,MAAM,QAAQ,KAAK,KACpB,aAAa,KAAK,KAClB,CAAC,aAAa,KAAK;ACLrB,IAAA,gBAAe,CAAC,UACd,SAAS,KAAK,KAAM,MAAgB,SAChC,gBAAiB,MAAgB,MAAM,IACpC,MAAgB,OAAO,UACvB,MAAgB,OAAO,QAC1B;ACVN,IAAA,oBAAe,CAAC,SACd,KAAK,UAAU,GAAG,KAAK,OAAO,aAAa,CAAC,KAAK;ACGnD,IAAA,qBAAe,CAAC,OAA+B,SAC7C,MAAM,IAAI,kBAAkB,IAAI,CAAC;ACHnC,IAAA,gBAAe,CAAC,eAAsB;AACpC,QAAM,gBACJ,WAAW,eAAe,WAAW,YAAY;AAEnD,SACE,SAAS,aAAa,KAAK,cAAc,eAAe,eAAe;AAE3E;ACTA,IAAA,QAAe,OAAO,WAAW,eAC/B,OAAO,OAAO,gBAAgB,eAC9B,OAAO,aAAa;ACEE,SAAA,YAAe,MAAO;AAC5C,MAAI;AACJ,QAAM,UAAU,MAAM,QAAQ,IAAI;AAClC,QAAM,qBACJ,OAAO,aAAa,cAAc,gBAAgB,WAAW;AAE/D,MAAI,gBAAgB,MAAM;AACxB,WAAO,IAAI,KAAK,IAAI;aACX,gBAAgB,KAAK;AAC9B,WAAO,IAAI,IAAI,IAAI;aAEnB,EAAE,UAAU,gBAAgB,QAAQ,yBACnC,WAAW,SAAS,IAAI,IACzB;AACA,WAAO,UAAU,CAAA,IAAK,CAAA;AAEtB,QAAI,CAAC,WAAW,CAAC,cAAc,IAAI,GAAG;AACpC,aAAO;WACF;AACL,iBAAW,OAAO,MAAM;AACtB,YAAI,KAAK,eAAe,GAAG,GAAG;AAC5B,eAAK,GAAG,IAAI,YAAY,KAAK,GAAG,CAAC;;;;SAIlC;AACL,WAAO;;AAGT,SAAO;AACT;AClCA,IAAA,UAAe,CAAS,UACtB,MAAM,QAAQ,KAAK,IAAI,MAAM,OAAO,OAAO,IAAI,CAAA;ACDjD,IAAA,cAAe,CAAC,QAAmC,QAAQ;ACK3D,IAAA,MAAe,CACb,QACA,MACA,iBACO;AACP,MAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,GAAG;AAC9B,WAAO;;AAGT,QAAM,SAAS,QAAQ,KAAK,MAAM,WAAW,CAAC,EAAE,OAC9C,CAACA,SAAQ,QACP,kBAAkBA,OAAM,IAAIA,UAASA,QAAO,GAAe,GAC7D,MAAM;AAGR,SAAO,YAAY,MAAM,KAAK,WAAW,SACrC,YAAY,OAAO,IAAe,CAAC,IACjC,eACA,OAAO,IAAe,IACxB;AACN;ACzBA,IAAA,YAAe,CAAC,UAAqC,OAAO,UAAU;ACAtE,IAAA,QAAe,CAAC,UAAkB,QAAQ,KAAK,KAAK;ACEpD,IAAA,eAAe,CAAC,UACd,QAAQ,MAAM,QAAQ,aAAa,EAAE,EAAE,MAAM,OAAO,CAAC;ACGvD,IAAA,MAAe,CACb,QACA,MACA,UACE;AACF,MAAI,QAAQ;AACZ,QAAM,WAAW,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,aAAa,IAAI;AACzD,QAAM,SAAS,SAAS;AACxB,QAAM,YAAY,SAAS;AAE3B,SAAO,EAAE,QAAQ,QAAQ;AACvB,UAAM,MAAM,SAAS,KAAK;AAC1B,QAAI,WAAW;AAEf,QAAI,UAAU,WAAW;AACvB,YAAM,WAAW,OAAO,GAAG;AAC3B,iBACE,SAAS,QAAQ,KAAK,MAAM,QAAQ,QAAQ,IACxC,WACA,CAAC,MAAM,CAAC,SAAS,QAAQ,CAAC,CAAC,IACzB,CAAA,IACA,CAAA;;AAGV,QAAI,QAAQ,eAAe,QAAQ,iBAAiB,QAAQ,aAAa;AACvE;;AAGF,WAAO,GAAG,IAAI;AACd,aAAS,OAAO,GAAG;;AAEvB;ACrCO,IAAM,SAAS;EACpB,MAAM;EACN,WAAW;EACX,QAAQ;;AAGH,IAAM,kBAAkB;EAC7B,QAAQ;EACR,UAAU;EACV,UAAU;EACV,WAAW;EACX,KAAK;;AAGA,IAAM,yBAAyB;EACpC,KAAK;EACL,KAAK;EACL,WAAW;EACX,WAAW;EACX,SAAS;EACT,UAAU;EACV,UAAU;;ACjBZ,IAAM,kBAAkBC,aAAAA,QAAM,cAAoC,IAAI;AAgC/D,IAAM,iBAAiB,MAK5BA,aAAAA,QAAM,WAAW,eAAe;AAoCrB,IAAA,eAAe,CAK1B,UACE;AACF,QAAM,EAAE,UAAU,GAAG,KAAI,IAAK;AAC9B,SACEA,aAAAA,QAAA,cAAC,gBAAgB,UAAQ,EAAC,OAAO,KAAgC,GAC9D,QAAQ;AAGf;ACvFA,IAAA,oBAAe,CAKb,WACA,SACA,qBACA,SAAS,SACP;AACF,QAAM,SAAS;IACb,eAAe,QAAQ;;AAGzB,aAAW,OAAO,WAAW;AAC3B,WAAO,eAAe,QAAQ,KAAK;MACjC,KAAK,MAAK;AACR,cAAM,OAAO;AAEb,YAAI,QAAQ,gBAAgB,IAAI,MAAM,gBAAgB,KAAK;AACzD,kBAAQ,gBAAgB,IAAI,IAAI,CAAC,UAAU,gBAAgB;;AAG7D,gCAAwB,oBAAoB,IAAI,IAAI;AACpD,eAAO,UAAU,IAAI;;IAExB,CAAA;;AAGH,SAAO;AACT;AC/BO,IAAM,4BACX,OAAO,WAAW,cAAoB,wBAAwB;ACkC1D,SAAU,aAId,OAA2D;AAE3D,QAAM,UAAU,eAAc;AAC9B,QAAM,EAAE,UAAU,QAAQ,SAAS,UAAU,MAAM,MAAK,IAAK,SAAS,CAAA;AACtE,QAAM,CAAC,WAAW,eAAe,IAAIA,aAAAA,QAAM,SAAS,QAAQ,UAAU;AACtE,QAAM,uBAAuBA,aAAAA,QAAM,OAAO;IACxC,SAAS;IACT,WAAW;IACX,aAAa;IACb,eAAe;IACf,kBAAkB;IAClB,cAAc;IACd,SAAS;IACT,QAAQ;EACT,CAAA;AAED,4BACE,MACE,QAAQ,WAAW;IACjB;IACA,WAAW,qBAAqB;IAChC;IACA,UAAU,CAACC,eAAa;AACtB,OAAC,YACC,gBAAgB;QACd,GAAG,QAAQ;QACX,GAAGA;MACJ,CAAA;;GAEN,GACH,CAAC,MAAM,UAAU,KAAK,CAAC;AAGzBD,eAAAA,QAAM,UAAU,MAAK;AACnB,yBAAqB,QAAQ,WAAW,QAAQ,UAAU,IAAI;EAChE,GAAG,CAAC,OAAO,CAAC;AAEZ,SAAOA,aAAAA,QAAM,QACX,MACE,kBACE,WACA,SACA,qBAAqB,SACrB,KAAK,GAET,CAAC,WAAW,OAAO,CAAC;AAExB;ACxFA,IAAA,WAAe,CAAC,UAAoC,OAAO,UAAU;ACIrE,IAAA,sBAAe,CACb,OACA,QACA,YACA,UACA,iBACE;AACF,MAAI,SAAS,KAAK,GAAG;AACnB,gBAAY,OAAO,MAAM,IAAI,KAAK;AAClC,WAAO,IAAI,YAAY,OAAO,YAAY;;AAG5C,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,MAAM,IACX,CAAC,eACC,YAAY,OAAO,MAAM,IAAI,SAAS,GAAG,IAAI,YAAY,SAAS,EACnE;;AAIL,eAAa,OAAO,WAAW;AAE/B,SAAO;AACT;ACqHM,SAAU,SACd,OAAmC;AAEnC,QAAM,UAAU,eAAc;AAC9B,QAAM,EACJ,UAAU,QAAQ,SAClB,MACA,cACA,UACA,MAAK,IACH,SAAS,CAAA;AACb,QAAM,gBAAgBA,aAAAA,QAAM,OAAO,YAAY;AAC/C,QAAM,CAAC,OAAO,WAAW,IAAIA,aAAAA,QAAM,SACjC,QAAQ,UACN,MACA,cAAc,OAAgD,CAC/D;AAGH,4BACE,MACE,QAAQ,WAAW;IACjB;IACA,WAAW;MACT,QAAQ;IACT;IACD;IACA,UAAU,CAAC,cACT,CAAC,YACD,YACE,oBACE,MACA,QAAQ,QACR,UAAU,UAAU,QAAQ,aAC5B,OACA,cAAc,OAAO,CACtB;GAEN,GACH,CAAC,MAAM,SAAS,UAAU,KAAK,CAAC;AAGlCA,eAAAA,QAAM,UAAU,MAAM,QAAQ,iBAAgB,CAAE;AAEhD,SAAO;AACT;AC7IM,SAAU,cAKd,OAAkE;AAElE,QAAM,UAAU,eAAc;AAC9B,QAAM,EAAE,MAAM,UAAU,UAAU,QAAQ,SAAS,iBAAgB,IAAK;AACxE,QAAM,eAAe,mBAAmB,QAAQ,OAAO,OAAO,IAAI;AAClE,QAAM,QAAQ,SAAS;IACrB;IACA;IACA,cAAc,IACZ,QAAQ,aACR,MACA,IAAI,QAAQ,gBAAgB,MAAM,MAAM,YAAY,CAAC;IAEvD,OAAO;EACR,CAAA;AACD,QAAM,YAAY,aAAa;IAC7B;IACA;IACA,OAAO;EACR,CAAA;AAED,QAAM,SAASA,aAAAA,QAAM,OAAO,KAAK;AACjC,QAAM,iBAAiBA,aAAAA,QAAM,OAC3B,QAAQ,SAAS,MAAM;IACrB,GAAG,MAAM;IACT;IACA,GAAI,UAAU,MAAM,QAAQ,IAAI,EAAE,UAAU,MAAM,SAAQ,IAAK,CAAA;EAChE,CAAA,CAAC;AAGJ,QAAM,aAAaA,aAAAA,QAAM,QACvB,MACE,OAAO,iBACL,CAAA,GACA;IACE,SAAS;MACP,YAAY;MACZ,KAAK,MAAM,CAAC,CAAC,IAAI,UAAU,QAAQ,IAAI;IACxC;IACD,SAAS;MACP,YAAY;MACZ,KAAK,MAAM,CAAC,CAAC,IAAI,UAAU,aAAa,IAAI;IAC7C;IACD,WAAW;MACT,YAAY;MACZ,KAAK,MAAM,CAAC,CAAC,IAAI,UAAU,eAAe,IAAI;IAC/C;IACD,cAAc;MACZ,YAAY;MACZ,KAAK,MAAM,CAAC,CAAC,IAAI,UAAU,kBAAkB,IAAI;IAClD;IACD,OAAO;MACL,YAAY;MACZ,KAAK,MAAM,IAAI,UAAU,QAAQ,IAAI;IACtC;EACF,CAAA,GAEL,CAAC,WAAW,IAAI,CAAC;AAGnB,QAAM,WAAWA,aAAAA,QAAM,YACrB,CAAC,UACC,eAAe,QAAQ,SAAS;IAC9B,QAAQ;MACN,OAAO,cAAc,KAAK;MAC1B;IACD;IACD,MAAM,OAAO;EACd,CAAA,GACH,CAAC,IAAI,CAAC;AAGR,QAAM,SAASA,aAAAA,QAAM,YACnB,MACE,eAAe,QAAQ,OAAO;IAC5B,QAAQ;MACN,OAAO,IAAI,QAAQ,aAAa,IAAI;MACpC;IACD;IACD,MAAM,OAAO;GACd,GACH,CAAC,MAAM,QAAQ,WAAW,CAAC;AAG7B,QAAM,MAAMA,aAAAA,QAAM,YAChB,CAAC,QAAY;AACX,UAAME,SAAQ,IAAI,QAAQ,SAAS,IAAI;AAEvC,QAAIA,UAAS,KAAK;AAChB,MAAAA,OAAM,GAAG,MAAM;QACb,OAAO,MAAM,IAAI,SAAS,IAAI,MAAK;QACnC,QAAQ,MAAM,IAAI,UAAU,IAAI,OAAM;QACtC,mBAAmB,CAAC,YAClB,IAAI,kBAAkB,OAAO;QAC/B,gBAAgB,MAAM,IAAI,eAAc;;;KAI9C,CAAC,QAAQ,SAAS,IAAI,CAAC;AAGzB,QAAM,QAAQF,aAAAA,QAAM,QAClB,OAAO;IACL;IACA;IACA,GAAI,UAAU,QAAQ,KAAK,UAAU,WACjC,EAAE,UAAU,UAAU,YAAY,SAAQ,IAC1C,CAAA;IACJ;IACA;IACA;EACD,IACD,CAAC,MAAM,UAAU,UAAU,UAAU,UAAU,QAAQ,KAAK,KAAK,CAAC;AAGpEA,eAAAA,QAAM,UAAU,MAAK;AACnB,UAAM,yBACJ,QAAQ,SAAS,oBAAoB;AAEvC,YAAQ,SAAS,MAAM;MACrB,GAAG,OAAO,QAAQ;MAClB,GAAI,UAAU,OAAO,QAAQ,QAAQ,IACjC,EAAE,UAAU,OAAO,QAAQ,SAAQ,IACnC,CAAA;IACL,CAAA;AAED,UAAM,gBAAgB,CAACG,OAAyBC,WAAkB;AAChE,YAAMF,SAAe,IAAI,QAAQ,SAASC,KAAI;AAE9C,UAAID,UAASA,OAAM,IAAI;AACrB,QAAAA,OAAM,GAAG,QAAQE;;IAErB;AAEA,kBAAc,MAAM,IAAI;AAExB,QAAI,wBAAwB;AAC1B,YAAMA,SAAQ,YAAY,IAAI,QAAQ,SAAS,eAAe,IAAI,CAAC;AACnE,UAAI,QAAQ,gBAAgB,MAAMA,MAAK;AACvC,UAAI,YAAY,IAAI,QAAQ,aAAa,IAAI,CAAC,GAAG;AAC/C,YAAI,QAAQ,aAAa,MAAMA,MAAK;;;AAIxC,KAAC,gBAAgB,QAAQ,SAAS,IAAI;AAEtC,WAAO,MAAK;AACV,OACE,eACI,0BAA0B,CAAC,QAAQ,OAAO,SAC1C,0BAEF,QAAQ,WAAW,IAAI,IACvB,cAAc,MAAM,KAAK;IAC/B;KACC,CAAC,MAAM,SAAS,cAAc,gBAAgB,CAAC;AAElDJ,eAAAA,QAAM,UAAU,MAAK;AACnB,YAAQ,kBAAkB;MACxB;MACA;IACD,CAAA;KACA,CAAC,UAAU,MAAM,OAAO,CAAC;AAE5B,SAAOA,aAAAA,QAAM,QACX,OAAO;IACL;IACA;IACA;MAEF,CAAC,OAAO,WAAW,UAAU,CAAC;AAElC;ACpLA,IAAM,aAAa,CAKjB,UAEA,MAAM,OAAO,cAAuD,KAAK,CAAC;AChDrE,IAAM,UAAU,CAAC,QAAoB;AAC1C,QAAM,SAAsB,CAAA;AAE5B,aAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AAClC,QAAI,aAAa,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,MAAM;AAC/C,YAAM,SAAS,QAAQ,IAAI,GAAG,CAAC;AAE/B,iBAAW,aAAa,OAAO,KAAK,MAAM,GAAG;AAC3C,eAAO,GAAG,GAAG,IAAI,SAAS,EAAE,IAAI,OAAO,SAAS;;WAE7C;AACL,aAAO,GAAG,IAAI,IAAI,GAAG;;;AAIzB,SAAO;AACT;ACdA,IAAM,eAAe;AAwBrB,SAAS,KAGP,OAAkD;AAClD,QAAM,UAAU,eAAc;AAC9B,QAAM,CAAC,SAAS,UAAU,IAAIA,aAAAA,QAAM,SAAS,KAAK;AAClD,QAAM,EACJ,UAAU,QAAQ,SAClB,UACA,UACA,QACA,SAAS,cACT,SACA,SACA,SACA,QACA,WACA,gBACA,GAAG,KAAI,IACL;AAEJ,QAAM,SAAS,OAAO,UAAoC;AACxD,QAAI,WAAW;AACf,QAAI,OAAO;AAEX,UAAM,QAAQ,aAAa,OAAO,SAAQ;AACxC,YAAM,WAAW,IAAI,SAAQ;AAC7B,UAAI,eAAe;AAEnB,UAAI;AACF,uBAAe,KAAK,UAAU,IAAI;eAC5B,IAAA;MAAA;AAER,YAAM,oBAAoB,QAAQ,QAAQ,WAAW;AAErD,iBAAW,OAAO,mBAAmB;AACnC,iBAAS,OAAO,KAAK,kBAAkB,GAAG,CAAC;;AAG7C,UAAI,UAAU;AACZ,cAAM,SAAS;UACb;UACA;UACA;UACA;UACA;QACD,CAAA;;AAGH,UAAI,QAAQ;AACV,YAAI;AACF,gBAAM,gCAAgC;YACpC,WAAW,QAAQ,cAAc;YACjC;UACD,EAAC,KAAK,CAAC,UAAU,SAAS,MAAM,SAAS,MAAM,CAAC;AAEjD,gBAAM,WAAW,MAAM,MAAM,OAAO,MAAM,GAAG;YAC3C;YACA,SAAS;cACP,GAAG;cACH,GAAI,UAAU,EAAE,gBAAgB,QAAO,IAAK,CAAA;YAC7C;YACD,MAAM,gCAAgC,eAAe;UACtD,CAAA;AAED,cACE,aACC,iBACG,CAAC,eAAe,SAAS,MAAM,IAC/B,SAAS,SAAS,OAAO,SAAS,UAAU,MAChD;AACA,uBAAW;AACX,uBAAW,QAAQ,EAAE,SAAQ,CAAE;AAC/B,mBAAO,OAAO,SAAS,MAAM;iBACxB;AACL,yBAAa,UAAU,EAAE,SAAQ,CAAE;;iBAE9B,OAAgB;AACvB,qBAAW;AACX,qBAAW,QAAQ,EAAE,MAAK,CAAE;;;IAGlC,CAAC,EAAE,KAAK;AAER,QAAI,YAAY,MAAM,SAAS;AAC7B,YAAM,QAAQ,UAAU,MAAM,KAAK;QACjC,oBAAoB;MACrB,CAAA;AACD,YAAM,QAAQ,SAAS,eAAe;QACpC;MACD,CAAA;;EAEL;AAEAA,eAAAA,QAAM,UAAU,MAAK;AACnB,eAAW,IAAI;KACd,CAAA,CAAE;AAEL,SAAO,SACLA,aAAAA,QAAA,cAAAA,aAAAA,QAAA,UAAA,MACG,OAAO;IACN;EACD,CAAA,CAAC,IAGJA,aAAAA,QAAA,cAAA,QAAA,EACE,YAAY,SACZ,QACA,QACA,SACA,UAAU,QAAM,GACZ,KAAI,GAEP,QAAQ;AAGf;AC5IA,IAAA,eAAe,CACb,MACA,0BACA,QACA,MACA,YAEA,2BACI;EACE,GAAG,OAAO,IAAI;EACd,OAAO;IACL,GAAI,OAAO,IAAI,KAAK,OAAO,IAAI,EAAG,QAAQ,OAAO,IAAI,EAAG,QAAQ,CAAA;IAChE,CAAC,IAAI,GAAG,WAAW;EACpB;AACF,IACD,CAAA;ACrBN,IAAA,wBAAe,CAAI,UAAc,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;ACgBtE,IAAA,gBAAe,MAAoB;AACjC,MAAI,aAA4B,CAAA;AAEhC,QAAM,OAAO,CAAC,UAAY;AACxB,eAAW,YAAY,YAAY;AACjC,eAAS,QAAQ,SAAS,KAAK,KAAK;;EAExC;AAEA,QAAM,YAAY,CAAC,aAAuC;AACxD,eAAW,KAAK,QAAQ;AACxB,WAAO;MACL,aAAa,MAAK;AAChB,qBAAa,WAAW,OAAO,CAAC,MAAM,MAAM,QAAQ;;;EAG1D;AAEA,QAAM,cAAc,MAAK;AACvB,iBAAa,CAAA;EACf;AAEA,SAAO;IACL,IAAI,YAAS;AACX,aAAO;;IAET;IACA;IACA;;AAEJ;ACzCA,IAAA,cAAe,CAAC,UACd,kBAAkB,KAAK,KAAK,CAAC,aAAa,KAAK;ACDnC,SAAU,UAAU,SAAc,SAAY;AAC1D,MAAI,YAAY,OAAO,KAAK,YAAY,OAAO,GAAG;AAChD,WAAO,YAAY;;AAGrB,MAAI,aAAa,OAAO,KAAK,aAAa,OAAO,GAAG;AAClD,WAAO,QAAQ,QAAO,MAAO,QAAQ,QAAO;;AAG9C,QAAM,QAAQ,OAAO,KAAK,OAAO;AACjC,QAAM,QAAQ,OAAO,KAAK,OAAO;AAEjC,MAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,WAAO;;AAGT,aAAW,OAAO,OAAO;AACvB,UAAM,OAAO,QAAQ,GAAG;AAExB,QAAI,CAAC,MAAM,SAAS,GAAG,GAAG;AACxB,aAAO;;AAGT,QAAI,QAAQ,OAAO;AACjB,YAAM,OAAO,QAAQ,GAAG;AAExB,UACG,aAAa,IAAI,KAAK,aAAa,IAAI,KACvC,SAAS,IAAI,KAAK,SAAS,IAAI,KAC/B,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI,IACtC,CAAC,UAAU,MAAM,IAAI,IACrB,SAAS,MACb;AACA,eAAO;;;;AAKb,SAAO;AACT;ACxCA,IAAA,gBAAe,CAAC,UACd,SAAS,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE;ACHzC,IAAA,cAAe,CAAC,YACd,QAAQ,SAAS;ACHnB,IAAA,aAAe,CAAC,UACd,OAAO,UAAU;ACCnB,IAAA,gBAAe,CAAC,UAAwC;AACtD,MAAI,CAAC,OAAO;AACV,WAAO;;AAGT,QAAM,QAAQ,QAAU,MAAsB,gBAA6B;AAC3E,SACE,kBACC,SAAS,MAAM,cAAc,MAAM,YAAY,cAAc;AAElE;ACVA,IAAA,mBAAe,CAAC,YACd,QAAQ,SAAS;ACDnB,IAAA,eAAe,CAAC,YACd,QAAQ,SAAS;ACEnB,IAAA,oBAAe,CAAC,QACd,aAAa,GAAG,KAAK,gBAAgB,GAAG;ACF1C,IAAA,OAAe,CAAC,QAAa,cAAc,GAAG,KAAK,IAAI;ACEvD,SAAS,QAAQ,QAAa,YAA+B;AAC3D,QAAM,SAAS,WAAW,MAAM,GAAG,EAAE,EAAE;AACvC,MAAI,QAAQ;AAEZ,SAAO,QAAQ,QAAQ;AACrB,aAAS,YAAY,MAAM,IAAI,UAAU,OAAO,WAAW,OAAO,CAAC;;AAGrE,SAAO;AACT;AAEA,SAAS,aAAa,KAAc;AAClC,aAAW,OAAO,KAAK;AACrB,QAAI,IAAI,eAAe,GAAG,KAAK,CAAC,YAAY,IAAI,GAAG,CAAC,GAAG;AACrD,aAAO;;;AAGX,SAAO;AACT;AAEc,SAAU,MAAM,QAAa,MAAkC;AAC3E,QAAM,QAAQ,MAAM,QAAQ,IAAI,IAC5B,OACA,MAAM,IAAI,IACR,CAAC,IAAI,IACL,aAAa,IAAI;AAEvB,QAAM,cAAc,MAAM,WAAW,IAAI,SAAS,QAAQ,QAAQ,KAAK;AAEvE,QAAM,QAAQ,MAAM,SAAS;AAC7B,QAAM,MAAM,MAAM,KAAK;AAEvB,MAAI,aAAa;AACf,WAAO,YAAY,GAAG;;AAGxB,MACE,UAAU,MACR,SAAS,WAAW,KAAK,cAAc,WAAW,KACjD,MAAM,QAAQ,WAAW,KAAK,aAAa,WAAW,IACzD;AACA,UAAM,QAAQ,MAAM,MAAM,GAAG,EAAE,CAAC;;AAGlC,SAAO;AACT;ACjDA,IAAA,oBAAe,CAAI,SAAoB;AACrC,aAAW,OAAO,MAAM;AACtB,QAAI,WAAW,KAAK,GAAG,CAAC,GAAG;AACzB,aAAO;;;AAGX,SAAO;AACT;ACFA,SAAS,gBAAmB,MAAS,SAA8B,CAAA,GAAE;AACnE,QAAM,oBAAoB,MAAM,QAAQ,IAAI;AAE5C,MAAI,SAAS,IAAI,KAAK,mBAAmB;AACvC,eAAW,OAAO,MAAM;AACtB,UACE,MAAM,QAAQ,KAAK,GAAG,CAAC,KACtB,SAAS,KAAK,GAAG,CAAC,KAAK,CAAC,kBAAkB,KAAK,GAAG,CAAC,GACpD;AACA,eAAO,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAA,IAAK,CAAA;AAC9C,wBAAgB,KAAK,GAAG,GAAG,OAAO,GAAG,CAAC;iBAC7B,CAAC,kBAAkB,KAAK,GAAG,CAAC,GAAG;AACxC,eAAO,GAAG,IAAI;;;;AAKpB,SAAO;AACT;AAEA,SAAS,gCACP,MACA,YACA,uBAGC;AAED,QAAM,oBAAoB,MAAM,QAAQ,IAAI;AAE5C,MAAI,SAAS,IAAI,KAAK,mBAAmB;AACvC,eAAW,OAAO,MAAM;AACtB,UACE,MAAM,QAAQ,KAAK,GAAG,CAAC,KACtB,SAAS,KAAK,GAAG,CAAC,KAAK,CAAC,kBAAkB,KAAK,GAAG,CAAC,GACpD;AACA,YACE,YAAY,UAAU,KACtB,YAAY,sBAAsB,GAAG,CAAC,GACtC;AACA,gCAAsB,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,IAChD,gBAAgB,KAAK,GAAG,GAAG,CAAA,CAAE,IAC7B,EAAE,GAAG,gBAAgB,KAAK,GAAG,CAAC,EAAC;eAC9B;AACL,0CACE,KAAK,GAAG,GACR,kBAAkB,UAAU,IAAI,CAAA,IAAK,WAAW,GAAG,GACnD,sBAAsB,GAAG,CAAC;;aAGzB;AACL,8BAAsB,GAAG,IAAI,CAAC,UAAU,KAAK,GAAG,GAAG,WAAW,GAAG,CAAC;;;;AAKxE,SAAO;AACT;AAEA,IAAA,iBAAe,CAAI,eAAkB,eACnC,gCACE,eACA,YACA,gBAAgB,UAAU,CAAC;AC/D/B,IAAM,gBAAqC;EACzC,OAAO;EACP,SAAS;;AAGX,IAAM,cAAc,EAAE,OAAO,MAAM,SAAS,KAAI;AAEhD,IAAA,mBAAe,CAAC,YAAqD;AACnE,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,QAAI,QAAQ,SAAS,GAAG;AACtB,YAAM,SAAS,QACZ,OAAO,CAAC,WAAW,UAAU,OAAO,WAAW,CAAC,OAAO,QAAQ,EAC/D,IAAI,CAAC,WAAW,OAAO,KAAK;AAC/B,aAAO,EAAE,OAAO,QAAQ,SAAS,CAAC,CAAC,OAAO,OAAM;;AAGlD,WAAO,QAAQ,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,EAAE;;MAErC,QAAQ,CAAC,EAAE,cAAc,CAAC,YAAY,QAAQ,CAAC,EAAE,WAAW,KAAK,IAC/D,YAAY,QAAQ,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,EAAE,UAAU,KACpD,cACA,EAAE,OAAO,QAAQ,CAAC,EAAE,OAAO,SAAS,KAAI,IAC1C;QACF;;AAGN,SAAO;AACT;AC9BA,IAAA,kBAAe,CACb,OACA,EAAE,eAAe,aAAa,WAAU,MAExC,YAAY,KAAK,IACb,QACA,gBACE,UAAU,KACR,MACA,QACE,CAAC,QACD,QACJ,eAAe,SAAS,KAAK,IAC3B,IAAI,KAAK,KAAK,IACd,aACE,WAAW,KAAK,IAChB;ACfZ,IAAM,gBAAkC;EACtC,SAAS;EACT,OAAO;;AAGT,IAAA,gBAAe,CAAC,YACd,MAAM,QAAQ,OAAO,IACjB,QAAQ,OACN,CAAC,UAAU,WACT,UAAU,OAAO,WAAW,CAAC,OAAO,WAChC;EACE,SAAS;EACT,OAAO,OAAO;AACf,IACD,UACN,aAAa,IAEf;ACXkB,SAAA,cAAc,IAAe;AACnD,QAAM,MAAM,GAAG;AAEf,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO,IAAI;;AAGb,MAAI,aAAa,GAAG,GAAG;AACrB,WAAO,cAAc,GAAG,IAAI,EAAE;;AAGhC,MAAI,iBAAiB,GAAG,GAAG;AACzB,WAAO,CAAC,GAAG,IAAI,eAAe,EAAE,IAAI,CAAC,EAAE,MAAK,MAAO,KAAK;;AAG1D,MAAIK,gBAAW,GAAG,GAAG;AACnB,WAAO,iBAAiB,GAAG,IAAI,EAAE;;AAGnC,SAAO,gBAAgB,YAAY,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,IAAI,OAAO,EAAE;AAC9E;ACpBA,IAAA,qBAAe,CACb,aACA,SACA,cACA,8BACE;AACF,QAAM,SAAiD,CAAA;AAEvD,aAAW,QAAQ,aAAa;AAC9B,UAAM,QAAe,IAAI,SAAS,IAAI;AAEtC,aAAS,IAAI,QAAQ,MAAM,MAAM,EAAE;;AAGrC,SAAO;IACL;IACA,OAAO,CAAC,GAAG,WAAW;IACtB;IACA;;AAEJ;AC/BA,IAAA,UAAe,CAAC,UAAoC,iBAAiB;ACSrE,IAAA,eAAe,CACb,SAEA,YAAY,IAAI,IACZ,OACA,QAAQ,IAAI,IACV,KAAK,SACL,SAAS,IAAI,IACX,QAAQ,KAAK,KAAK,IAChB,KAAK,MAAM,SACX,KAAK,QACP;ACjBV,IAAA,qBAAe,CAAC,UAAsC;EACpD,YAAY,CAAC,QAAQ,SAAS,gBAAgB;EAC9C,UAAU,SAAS,gBAAgB;EACnC,YAAY,SAAS,gBAAgB;EACrC,SAAS,SAAS,gBAAgB;EAClC,WAAW,SAAS,gBAAgB;AACrC;ACLD,IAAM,iBAAiB;AAEvB,IAAA,uBAAe,CAAC,mBACd,CAAC,CAAC,kBACF,CAAC,CAAC,eAAe,YACjB,CAAC,EACE,WAAW,eAAe,QAAQ,KACjC,eAAe,SAAS,YAAY,SAAS,kBAC9C,SAAS,eAAe,QAAQ,KAC/B,OAAO,OAAO,eAAe,QAAQ,EAAE,KACrC,CAAC,qBACC,iBAAiB,YAAY,SAAS,cAAc;ACb9D,IAAA,gBAAe,CAAC,YACd,QAAQ,UACP,QAAQ,YACP,QAAQ,OACR,QAAQ,OACR,QAAQ,aACR,QAAQ,aACR,QAAQ,WACR,QAAQ;ACRZ,IAAA,YAAe,CACb,MACA,QACA,gBAEA,CAAC,gBACA,OAAO,YACN,OAAO,MAAM,IAAI,IAAI,KACrB,CAAC,GAAG,OAAO,KAAK,EAAE,KAChB,CAAC,cACC,KAAK,WAAW,SAAS,KACzB,SAAS,KAAK,KAAK,MAAM,UAAU,MAAM,CAAC,CAAC;ACTnD,IAAM,wBAAwB,CAC5B,QACA,QACA,aACA,eACE;AACF,aAAW,OAAO,eAAe,OAAO,KAAK,MAAM,GAAG;AACpD,UAAM,QAAQ,IAAI,QAAQ,GAAG;AAE7B,QAAI,OAAO;AACT,YAAM,EAAE,IAAI,GAAG,aAAY,IAAK;AAEhC,UAAI,IAAI;AACN,YAAI,GAAG,QAAQ,GAAG,KAAK,CAAC,KAAK,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY;AACnE,iBAAO;mBACE,GAAG,OAAO,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,YAAY;AAC3D,iBAAO;eACF;AACL,cAAI,sBAAsB,cAAc,MAAM,GAAG;AAC/C;;;iBAGK,SAAS,YAAY,GAAG;AACjC,YAAI,sBAAsB,cAA2B,MAAM,GAAG;AAC5D;;;;;AAKR;AACF;AC9BwB,SAAA,kBACtB,QACA,SACA,MAAY;AAKZ,QAAM,QAAQ,IAAI,QAAQ,IAAI;AAE9B,MAAI,SAAS,MAAM,IAAI,GAAG;AACxB,WAAO;MACL;MACA;;;AAIJ,QAAM,QAAQ,KAAK,MAAM,GAAG;AAE5B,SAAO,MAAM,QAAQ;AACnB,UAAM,YAAY,MAAM,KAAK,GAAG;AAChC,UAAM,QAAQ,IAAI,SAAS,SAAS;AACpC,UAAM,aAAa,IAAI,QAAQ,SAAS;AAExC,QAAI,SAAS,CAAC,MAAM,QAAQ,KAAK,KAAK,SAAS,WAAW;AACxD,aAAO,EAAE,KAAI;;AAGf,QAAI,cAAc,WAAW,MAAM;AACjC,aAAO;QACL,MAAM;QACN,OAAO;;;AAIX,QAAI,cAAc,WAAW,QAAQ,WAAW,KAAK,MAAM;AACzD,aAAO;QACL,MAAM,GAAG,SAAS;QAClB,OAAO,WAAW;;;AAItB,UAAM,IAAG;;AAGX,SAAO;IACL;;AAEJ;AC3CA,IAAA,wBAAe,CACb,eAIA,iBACA,iBACA,WACE;AACF,kBAAgB,aAAa;AAC7B,QAAM,EAAE,MAAM,GAAG,UAAS,IAAK;AAE/B,SACE,cAAc,SAAS,KACvB,OAAO,KAAK,SAAS,EAAE,UAAU,OAAO,KAAK,eAAe,EAAE,UAC9D,OAAO,KAAK,SAAS,EAAE,KACrB,CAAC,QACC,gBAAgB,GAA0B,OACzC,CAAC,UAAU,gBAAgB,IAAI;AAGxC;AC5BA,IAAA,wBAAe,CACb,MACA,YACA,UAEA,CAAC,QACD,CAAC,cACD,SAAS,cACT,sBAAsB,IAAI,EAAE,KAC1B,CAAC,gBACC,gBACC,QACG,gBAAgB,aAChB,YAAY,WAAW,UAAU,KACjC,WAAW,WAAW,WAAW,EAAE;ACd7C,IAAA,iBAAe,CACb,aACA,WACA,aACA,gBAIA,SACE;AACF,MAAI,KAAK,SAAS;AAChB,WAAO;aACE,CAAC,eAAe,KAAK,WAAW;AACzC,WAAO,EAAE,aAAa;aACb,cAAc,eAAe,WAAW,KAAK,UAAU;AAChE,WAAO,CAAC;aACC,cAAc,eAAe,aAAa,KAAK,YAAY;AACpE,WAAO;;AAET,SAAO;AACT;AClBA,IAAA,kBAAe,CAAI,KAAQ,SACzB,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI;ACKpD,IAAA,4BAAe,CACb,QACA,OACA,SACkB;AAClB,QAAM,mBAAmB,sBAAsB,IAAI,QAAQ,IAAI,CAAC;AAChE,MAAI,kBAAkB,QAAQ,MAAM,IAAI,CAAC;AACzC,MAAI,QAAQ,MAAM,gBAAgB;AAClC,SAAO;AACT;AChBA,IAAA,YAAe,CAAC,UAAqC,SAAS,KAAK;ACCrD,SAAU,iBACtB,QACA,KACA,OAAO,YAAU;AAEjB,MACE,UAAU,MAAM,KACf,MAAM,QAAQ,MAAM,KAAK,OAAO,MAAM,SAAS,KAC/C,UAAU,MAAM,KAAK,CAAC,QACvB;AACA,WAAO;MACL;MACA,SAAS,UAAU,MAAM,IAAI,SAAS;MACtC;;;AAGN;AChBA,IAAA,qBAAe,CAAC,mBACd,SAAS,cAAc,KAAK,CAAC,QAAQ,cAAc,IAC/C,iBACA;EACE,OAAO;EACP,SAAS;;ACwBjB,IAAA,gBAAe,OACb,OACA,oBACA,YACA,0BACA,2BACA,iBACgC;AAChC,QAAM,EACJ,KACA,MACA,UACA,WACA,WACA,KACA,KACA,SACA,UACA,MACA,eACA,MAAK,IACH,MAAM;AACV,QAAM,aAA+B,IAAI,YAAY,IAAI;AACzD,MAAI,CAAC,SAAS,mBAAmB,IAAI,IAAI,GAAG;AAC1C,WAAO,CAAA;;AAET,QAAM,WAA6B,OAAO,KAAK,CAAC,IAAK;AACrD,QAAM,oBAAoB,CAAC,YAA8B;AACvD,QAAI,6BAA6B,SAAS,gBAAgB;AACxD,eAAS,kBAAkB,UAAU,OAAO,IAAI,KAAK,WAAW,EAAE;AAClE,eAAS,eAAc;;EAE3B;AACA,QAAM,QAA6B,CAAA;AACnC,QAAM,UAAU,aAAa,GAAG;AAChC,QAAM,aAAa,gBAAgB,GAAG;AACtC,QAAMC,qBAAoB,WAAW;AACrC,QAAM,WACF,iBAAiB,YAAY,GAAG,MAChC,YAAY,IAAI,KAAK,KACrB,YAAY,UAAU,KACvB,cAAc,GAAG,KAAK,IAAI,UAAU,MACrC,eAAe,MACd,MAAM,QAAQ,UAAU,KAAK,CAAC,WAAW;AAC5C,QAAM,oBAAoB,aAAa,KACrC,MACA,MACA,0BACA,KAAK;AAEP,QAAM,mBAAmB,CACvB,WACA,kBACA,kBACA,UAAmB,uBAAuB,WAC1C,UAAmB,uBAAuB,cACxC;AACF,UAAM,UAAU,YAAY,mBAAmB;AAC/C,UAAM,IAAI,IAAI;MACZ,MAAM,YAAY,UAAU;MAC5B;MACA;MACA,GAAG,kBAAkB,YAAY,UAAU,SAAS,OAAO;;EAE/D;AAEA,MACE,eACI,CAAC,MAAM,QAAQ,UAAU,KAAK,CAAC,WAAW,SAC1C,aACE,CAACA,uBAAsB,WAAW,kBAAkB,UAAU,MAC7D,UAAU,UAAU,KAAK,CAAC,cAC1B,cAAc,CAAC,iBAAiB,IAAI,EAAE,WACtC,WAAW,CAAC,cAAc,IAAI,EAAE,UACvC;AACA,UAAM,EAAE,OAAO,QAAO,IAAK,UAAU,QAAQ,IACzC,EAAE,OAAO,CAAC,CAAC,UAAU,SAAS,SAAQ,IACtC,mBAAmB,QAAQ;AAE/B,QAAI,OAAO;AACT,YAAM,IAAI,IAAI;QACZ,MAAM,uBAAuB;QAC7B;QACA,KAAK;QACL,GAAG,kBAAkB,uBAAuB,UAAU,OAAO;;AAE/D,UAAI,CAAC,0BAA0B;AAC7B,0BAAkB,OAAO;AACzB,eAAO;;;;AAKb,MAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,GAAG,IAAI;AACpE,QAAI;AACJ,QAAI;AACJ,UAAM,YAAY,mBAAmB,GAAG;AACxC,UAAM,YAAY,mBAAmB,GAAG;AAExC,QAAI,CAAC,kBAAkB,UAAU,KAAK,CAAC,MAAM,UAAoB,GAAG;AAClE,YAAM,cACH,IAAyB,kBACzB,aAAa,CAAC,aAAa;AAC9B,UAAI,CAAC,kBAAkB,UAAU,KAAK,GAAG;AACvC,oBAAY,cAAc,UAAU;;AAEtC,UAAI,CAAC,kBAAkB,UAAU,KAAK,GAAG;AACvC,oBAAY,cAAc,UAAU;;WAEjC;AACL,YAAM,YACH,IAAyB,eAAe,IAAI,KAAK,UAAoB;AACxE,YAAM,oBAAoB,CAAC,SACzB,oBAAI,MAAK,oBAAI,KAAI,GAAG,aAAY,IAAK,MAAM,IAAI;AACjD,YAAM,SAAS,IAAI,QAAQ;AAC3B,YAAM,SAAS,IAAI,QAAQ;AAE3B,UAAI,SAAS,UAAU,KAAK,KAAK,YAAY;AAC3C,oBAAY,SACR,kBAAkB,UAAU,IAAI,kBAAkB,UAAU,KAAK,IACjE,SACE,aAAa,UAAU,QACvB,YAAY,IAAI,KAAK,UAAU,KAAK;;AAG5C,UAAI,SAAS,UAAU,KAAK,KAAK,YAAY;AAC3C,oBAAY,SACR,kBAAkB,UAAU,IAAI,kBAAkB,UAAU,KAAK,IACjE,SACE,aAAa,UAAU,QACvB,YAAY,IAAI,KAAK,UAAU,KAAK;;;AAI9C,QAAI,aAAa,WAAW;AAC1B,uBACE,CAAC,CAAC,WACF,UAAU,SACV,UAAU,SACV,uBAAuB,KACvB,uBAAuB,GAAG;AAE5B,UAAI,CAAC,0BAA0B;AAC7B,0BAAkB,MAAM,IAAI,EAAG,OAAO;AACtC,eAAO;;;;AAKb,OACG,aAAa,cACd,CAAC,YACA,SAAS,UAAU,KAAM,gBAAgB,MAAM,QAAQ,UAAU,IAClE;AACA,UAAM,kBAAkB,mBAAmB,SAAS;AACpD,UAAM,kBAAkB,mBAAmB,SAAS;AACpD,UAAM,YACJ,CAAC,kBAAkB,gBAAgB,KAAK,KACxC,WAAW,SAAS,CAAC,gBAAgB;AACvC,UAAM,YACJ,CAAC,kBAAkB,gBAAgB,KAAK,KACxC,WAAW,SAAS,CAAC,gBAAgB;AAEvC,QAAI,aAAa,WAAW;AAC1B,uBACE,WACA,gBAAgB,SAChB,gBAAgB,OAAO;AAEzB,UAAI,CAAC,0BAA0B;AAC7B,0BAAkB,MAAM,IAAI,EAAG,OAAO;AACtC,eAAO;;;;AAKb,MAAI,WAAW,CAAC,WAAW,SAAS,UAAU,GAAG;AAC/C,UAAM,EAAE,OAAO,cAAc,QAAO,IAAK,mBAAmB,OAAO;AAEnE,QAAI,QAAQ,YAAY,KAAK,CAAC,WAAW,MAAM,YAAY,GAAG;AAC5D,YAAM,IAAI,IAAI;QACZ,MAAM,uBAAuB;QAC7B;QACA;QACA,GAAG,kBAAkB,uBAAuB,SAAS,OAAO;;AAE9D,UAAI,CAAC,0BAA0B;AAC7B,0BAAkB,OAAO;AACzB,eAAO;;;;AAKb,MAAI,UAAU;AACZ,QAAI,WAAW,QAAQ,GAAG;AACxB,YAAM,SAAS,MAAM,SAAS,YAAY,UAAU;AACpD,YAAM,gBAAgB,iBAAiB,QAAQ,QAAQ;AAEvD,UAAI,eAAe;AACjB,cAAM,IAAI,IAAI;UACZ,GAAG;UACH,GAAG,kBACD,uBAAuB,UACvB,cAAc,OAAO;;AAGzB,YAAI,CAAC,0BAA0B;AAC7B,4BAAkB,cAAc,OAAO;AACvC,iBAAO;;;eAGF,SAAS,QAAQ,GAAG;AAC7B,UAAI,mBAAmB,CAAA;AAEvB,iBAAW,OAAO,UAAU;AAC1B,YAAI,CAAC,cAAc,gBAAgB,KAAK,CAAC,0BAA0B;AACjE;;AAGF,cAAM,gBAAgB,iBACpB,MAAM,SAAS,GAAG,EAAE,YAAY,UAAU,GAC1C,UACA,GAAG;AAGL,YAAI,eAAe;AACjB,6BAAmB;YACjB,GAAG;YACH,GAAG,kBAAkB,KAAK,cAAc,OAAO;;AAGjD,4BAAkB,cAAc,OAAO;AAEvC,cAAI,0BAA0B;AAC5B,kBAAM,IAAI,IAAI;;;;AAKpB,UAAI,CAAC,cAAc,gBAAgB,GAAG;AACpC,cAAM,IAAI,IAAI;UACZ,KAAK;UACL,GAAG;;AAEL,YAAI,CAAC,0BAA0B;AAC7B,iBAAO;;;;;AAMf,oBAAkB,IAAI;AACtB,SAAO;AACT;ACpMA,IAAM,iBAAiB;EACrB,MAAM,gBAAgB;EACtB,gBAAgB,gBAAgB;EAChC,kBAAkB;;AAGJ,SAAA,kBAKd,QAAkE,CAAA,GAAE;AAUpE,MAAI,WAAW;IACb,GAAG;IACH,GAAG;;AAEL,MAAI,aAAsC;IACxC,aAAa;IACb,SAAS;IACT,SAAS;IACT,WAAW,WAAW,SAAS,aAAa;IAC5C,cAAc;IACd,aAAa;IACb,cAAc;IACd,oBAAoB;IACpB,SAAS;IACT,eAAe,CAAA;IACf,aAAa,CAAA;IACb,kBAAkB,CAAA;IAClB,QAAQ,SAAS,UAAU,CAAA;IAC3B,UAAU,SAAS,YAAY;;AAEjC,QAAM,UAAqB,CAAA;AAC3B,MAAI,iBACF,SAAS,SAAS,aAAa,KAAK,SAAS,SAAS,MAAM,IACxD,YAAY,SAAS,iBAAiB,SAAS,MAAM,KAAK,CAAA,IAC1D,CAAA;AACN,MAAI,cAAc,SAAS,mBACtB,CAAA,IACA,YAAY,cAAc;AAC/B,MAAI,SAAS;IACX,QAAQ;IACR,OAAO;IACP,OAAO;;AAET,MAAI,SAAgB;IAClB,OAAO,oBAAI,IAAG;IACd,UAAU,oBAAI,IAAG;IACjB,SAAS,oBAAI,IAAG;IAChB,OAAO,oBAAI,IAAG;IACd,OAAO,oBAAI,IAAG;;AAEhB,MAAI;AACJ,MAAI,QAAQ;AACZ,QAAM,kBAAiC;IACrC,SAAS;IACT,aAAa;IACb,kBAAkB;IAClB,eAAe;IACf,cAAc;IACd,SAAS;IACT,QAAQ;;AAEV,MAAI,2BAA2B;IAC7B,GAAG;;AAEL,QAAM,YAAoC;IACxC,OAAO,cAAa;IACpB,OAAO,cAAa;;AAGtB,QAAM,mCACJ,SAAS,iBAAiB,gBAAgB;AAE5C,QAAM,WACJ,CAAqB,aACrB,CAAC,SAAgB;AACf,iBAAa,KAAK;AAClB,YAAQ,WAAW,UAAU,IAAI;EACnC;AAEF,QAAM,YAAY,OAAO,sBAA+B;AACtD,QACE,CAAC,SAAS,aACT,gBAAgB,WACf,yBAAyB,WACzB,oBACF;AACA,YAAM,UAAU,SAAS,WACrB,eAAe,MAAM,WAAU,GAAI,MAAM,IACzC,MAAM,yBAAyB,SAAS,IAAI;AAEhD,UAAI,YAAY,WAAW,SAAS;AAClC,kBAAU,MAAM,KAAK;UACnB;QACD,CAAA;;;EAGP;AAEA,QAAM,sBAAsB,CAAC,OAAkB,iBAA0B;AACvE,QACE,CAAC,SAAS,aACT,gBAAgB,gBACf,gBAAgB,oBAChB,yBAAyB,gBACzB,yBAAyB,mBAC3B;AACA,OAAC,SAAS,MAAM,KAAK,OAAO,KAAK,GAAG,QAAQ,CAAC,SAAQ;AACnD,YAAI,MAAM;AACR,yBACI,IAAI,WAAW,kBAAkB,MAAM,YAAY,IACnD,MAAM,WAAW,kBAAkB,IAAI;;MAE/C,CAAC;AAED,gBAAU,MAAM,KAAK;QACnB,kBAAkB,WAAW;QAC7B,cAAc,CAAC,cAAc,WAAW,gBAAgB;MACzD,CAAA;;EAEL;AAEA,QAAM,iBAAwC,CAC5C,MACA,SAAS,CAAA,GACT,QACA,MACA,kBAAkB,MAClB,6BAA6B,SAC3B;AACF,QAAI,QAAQ,UAAU,CAAC,SAAS,UAAU;AACxC,aAAO,SAAS;AAChB,UAAI,8BAA8B,MAAM,QAAQ,IAAI,SAAS,IAAI,CAAC,GAAG;AACnE,cAAM,cAAc,OAAO,IAAI,SAAS,IAAI,GAAG,KAAK,MAAM,KAAK,IAAI;AACnE,2BAAmB,IAAI,SAAS,MAAM,WAAW;;AAGnD,UACE,8BACA,MAAM,QAAQ,IAAI,WAAW,QAAQ,IAAI,CAAC,GAC1C;AACA,cAAM,SAAS,OACb,IAAI,WAAW,QAAQ,IAAI,GAC3B,KAAK,MACL,KAAK,IAAI;AAEX,2BAAmB,IAAI,WAAW,QAAQ,MAAM,MAAM;AACtD,wBAAgB,WAAW,QAAQ,IAAI;;AAGzC,WACG,gBAAgB,iBACf,yBAAyB,kBAC3B,8BACA,MAAM,QAAQ,IAAI,WAAW,eAAe,IAAI,CAAC,GACjD;AACA,cAAM,gBAAgB,OACpB,IAAI,WAAW,eAAe,IAAI,GAClC,KAAK,MACL,KAAK,IAAI;AAEX,2BAAmB,IAAI,WAAW,eAAe,MAAM,aAAa;;AAGtE,UAAI,gBAAgB,eAAe,yBAAyB,aAAa;AACvE,mBAAW,cAAc,eAAe,gBAAgB,WAAW;;AAGrE,gBAAU,MAAM,KAAK;QACnB;QACA,SAAS,UAAU,MAAM,MAAM;QAC/B,aAAa,WAAW;QACxB,QAAQ,WAAW;QACnB,SAAS,WAAW;MACrB,CAAA;WACI;AACL,UAAI,aAAa,MAAM,MAAM;;EAEjC;AAEA,QAAM,eAAe,CAAC,MAAyB,UAAqB;AAClE,QAAI,WAAW,QAAQ,MAAM,KAAK;AAClC,cAAU,MAAM,KAAK;MACnB,QAAQ,WAAW;IACpB,CAAA;EACH;AAEA,QAAM,aAAa,CAAC,WAAqC;AACvD,eAAW,SAAS;AACpB,cAAU,MAAM,KAAK;MACnB,QAAQ,WAAW;MACnB,SAAS;IACV,CAAA;EACH;AAEA,QAAM,sBAAsB,CAC1B,MACA,sBACA,OACA,QACE;AACF,UAAM,QAAe,IAAI,SAAS,IAAI;AAEtC,QAAI,OAAO;AACT,YAAM,eAAe,IACnB,aACA,MACA,YAAY,KAAK,IAAI,IAAI,gBAAgB,IAAI,IAAI,KAAK;AAGxD,kBAAY,YAAY,KACvB,OAAQ,IAAyB,kBAClC,uBACI,IACE,aACA,MACA,uBAAuB,eAAe,cAAc,MAAM,EAAE,CAAC,IAE/D,cAAc,MAAM,YAAY;AAEpC,aAAO,SAAS,UAAS;;EAE7B;AAEA,QAAM,sBAAsB,CAC1B,MACA,YACA,aACA,aACA,iBAGE;AACF,QAAI,oBAAoB;AACxB,QAAI,kBAAkB;AACtB,UAAM,SAA8D;MAClE;;AAGF,QAAI,CAAC,SAAS,UAAU;AACtB,UAAI,CAAC,eAAe,aAAa;AAC/B,YAAI,gBAAgB,WAAW,yBAAyB,SAAS;AAC/D,4BAAkB,WAAW;AAC7B,qBAAW,UAAU,OAAO,UAAU,UAAS;AAC/C,8BAAoB,oBAAoB,OAAO;;AAGjD,cAAM,yBAAyB,UAC7B,IAAI,gBAAgB,IAAI,GACxB,UAAU;AAGZ,0BAAkB,CAAC,CAAC,IAAI,WAAW,aAAa,IAAI;AACpD,iCACI,MAAM,WAAW,aAAa,IAAI,IAClC,IAAI,WAAW,aAAa,MAAM,IAAI;AAC1C,eAAO,cAAc,WAAW;AAChC,4BACE,sBACE,gBAAgB,eAChB,yBAAyB,gBACzB,oBAAoB,CAAC;;AAG3B,UAAI,aAAa;AACf,cAAM,yBAAyB,IAAI,WAAW,eAAe,IAAI;AAEjE,YAAI,CAAC,wBAAwB;AAC3B,cAAI,WAAW,eAAe,MAAM,WAAW;AAC/C,iBAAO,gBAAgB,WAAW;AAClC,8BACE,sBACE,gBAAgB,iBAChB,yBAAyB,kBACzB,2BAA2B;;;AAInC,2BAAqB,gBAAgB,UAAU,MAAM,KAAK,MAAM;;AAGlE,WAAO,oBAAoB,SAAS,CAAA;EACtC;AAEA,QAAM,sBAAsB,CAC1B,MACA,SACA,OACA,eAKE;AACF,UAAM,qBAAqB,IAAI,WAAW,QAAQ,IAAI;AACtD,UAAM,qBACH,gBAAgB,WAAW,yBAAyB,YACrD,UAAU,OAAO,KACjB,WAAW,YAAY;AAEzB,QAAI,SAAS,cAAc,OAAO;AAChC,2BAAqB,SAAS,MAAM,aAAa,MAAM,KAAK,CAAC;AAC7D,yBAAmB,SAAS,UAAU;WACjC;AACL,mBAAa,KAAK;AAClB,2BAAqB;AACrB,cACI,IAAI,WAAW,QAAQ,MAAM,KAAK,IAClC,MAAM,WAAW,QAAQ,IAAI;;AAGnC,SACG,QAAQ,CAAC,UAAU,oBAAoB,KAAK,IAAI,uBACjD,CAAC,cAAc,UAAU,KACzB,mBACA;AACA,YAAM,mBAAmB;QACvB,GAAG;QACH,GAAI,qBAAqB,UAAU,OAAO,IAAI,EAAE,QAAO,IAAK,CAAA;QAC5D,QAAQ,WAAW;QACnB;;AAGF,mBAAa;QACX,GAAG;QACH,GAAG;;AAGL,gBAAU,MAAM,KAAK,gBAAgB;;EAEzC;AAEA,QAAM,aAAa,OAAO,SAA8B;AACtD,wBAAoB,MAAM,IAAI;AAC9B,UAAM,SAAS,MAAM,SAAS,SAC5B,aACA,SAAS,SACT,mBACE,QAAQ,OAAO,OACf,SACA,SAAS,cACT,SAAS,yBAAyB,CACnC;AAEH,wBAAoB,IAAI;AACxB,WAAO;EACT;AAEA,QAAM,8BAA8B,OAAO,UAA+B;AACxE,UAAM,EAAE,OAAM,IAAK,MAAM,WAAW,KAAK;AAEzC,QAAI,OAAO;AACT,iBAAW,QAAQ,OAAO;AACxB,cAAM,QAAQ,IAAI,QAAQ,IAAI;AAC9B,gBACI,IAAI,WAAW,QAAQ,MAAM,KAAK,IAClC,MAAM,WAAW,QAAQ,IAAI;;WAE9B;AACL,iBAAW,SAAS;;AAGtB,WAAO;EACT;AAEA,QAAM,2BAA2B,OAC/B,QACA,sBACA,UAEI;IACF,OAAO;EACR,MACC;AACF,eAAW,QAAQ,QAAQ;AACzB,YAAM,QAAQ,OAAO,IAAI;AAEzB,UAAI,OAAO;AACT,cAAM,EAAE,IAAI,GAAG,WAAU,IAAK;AAE9B,YAAI,IAAI;AACN,gBAAM,mBAAmB,OAAO,MAAM,IAAI,GAAG,IAAI;AACjD,gBAAM,oBACJ,MAAM,MAAM,qBAAsB,MAAgB,EAAE;AAEtD,cAAI,qBAAqB,gBAAgB,kBAAkB;AACzD,gCAAoB,CAAC,IAAI,GAAG,IAAI;;AAGlC,gBAAM,aAAa,MAAM,cACvB,OACA,OAAO,UACP,aACA,kCACA,SAAS,6BAA6B,CAAC,sBACvC,gBAAgB;AAGlB,cAAI,qBAAqB,gBAAgB,kBAAkB;AACzD,gCAAoB,CAAC,IAAI,CAAC;;AAG5B,cAAI,WAAW,GAAG,IAAI,GAAG;AACvB,oBAAQ,QAAQ;AAChB,gBAAI,sBAAsB;AACxB;;;AAIJ,WAAC,yBACE,IAAI,YAAY,GAAG,IAAI,IACpB,mBACE,0BACE,WAAW,QACX,YACA,GAAG,IAAI,IAET,IAAI,WAAW,QAAQ,GAAG,MAAM,WAAW,GAAG,IAAI,CAAC,IACrD,MAAM,WAAW,QAAQ,GAAG,IAAI;;AAGxC,SAAC,cAAc,UAAU,KACtB,MAAM,yBACL,YACA,sBACA,OAAO;;;AAKf,WAAO,QAAQ;EACjB;AAEA,QAAM,mBAAmB,MAAK;AAC5B,eAAW,QAAQ,OAAO,SAAS;AACjC,YAAM,QAAe,IAAI,SAAS,IAAI;AAEtC,gBACG,MAAM,GAAG,OACN,MAAM,GAAG,KAAK,MAAM,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,IACvC,CAAC,KAAK,MAAM,GAAG,GAAG,MACtB,WAAW,IAA+B;;AAG9C,WAAO,UAAU,oBAAI,IAAG;EAC1B;AAEA,QAAM,YAAwB,CAAC,MAAM,SACnC,CAAC,SAAS,aACT,QAAQ,QAAQ,IAAI,aAAa,MAAM,IAAI,GAC5C,CAAC,UAAU,UAAS,GAAI,cAAc;AAExC,QAAM,YAAyC,CAC7C,OACA,cACA,aAEA,oBACE,OACA,QACA;IACE,GAAI,OAAO,QACP,cACA,YAAY,YAAY,IACtB,iBACA,SAAS,KAAK,IACZ,EAAE,CAAC,KAAK,GAAG,aAAY,IACvB;EACT,GACD,UACA,YAAY;AAGhB,QAAM,iBAAiB,CACrB,SAEA,QACE,IACE,OAAO,QAAQ,cAAc,gBAC7B,MACA,SAAS,mBAAmB,IAAI,gBAAgB,MAAM,CAAA,CAAE,IAAI,CAAA,CAAE,CAC/D;AAGL,QAAM,gBAAgB,CACpB,MACA,OACA,UAA0B,CAAA,MACxB;AACF,UAAM,QAAe,IAAI,SAAS,IAAI;AACtC,QAAI,aAAsB;AAE1B,QAAI,OAAO;AACT,YAAM,iBAAiB,MAAM;AAE7B,UAAI,gBAAgB;AAClB,SAAC,eAAe,YACd,IAAI,aAAa,MAAM,gBAAgB,OAAO,cAAc,CAAC;AAE/D,qBACE,cAAc,eAAe,GAAG,KAAK,kBAAkB,KAAK,IACxD,KACA;AAEN,YAAI,iBAAiB,eAAe,GAAG,GAAG;AACxC,WAAC,GAAG,eAAe,IAAI,OAAO,EAAE,QAC9B,CAAC,cACE,UAAU,WACT,WACA,SAAS,UAAU,KAAK,CAAE;mBAEvB,eAAe,MAAM;AAC9B,cAAI,gBAAgB,eAAe,GAAG,GAAG;AACvC,2BAAe,KAAK,QAAQ,CAAC,gBAAe;AAC1C,kBAAI,CAAC,YAAY,kBAAkB,CAAC,YAAY,UAAU;AACxD,oBAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,8BAAY,UAAU,CAAC,CAAC,WAAW,KACjC,CAAC,SAAiB,SAAS,YAAY,KAAK;uBAEzC;AACL,8BAAY,UACV,eAAe,YAAY,SAAS,CAAC,CAAC;;;YAG9C,CAAC;iBACI;AACL,2BAAe,KAAK,QAClB,CAAC,aACE,SAAS,UAAU,SAAS,UAAU,UAAW;;mBAG/C,YAAY,eAAe,GAAG,GAAG;AAC1C,yBAAe,IAAI,QAAQ;eACtB;AACL,yBAAe,IAAI,QAAQ;AAE3B,cAAI,CAAC,eAAe,IAAI,MAAM;AAC5B,sBAAU,MAAM,KAAK;cACnB;cACA,QAAQ,YAAY,WAAW;YAChC,CAAA;;;;;AAMT,KAAC,QAAQ,eAAe,QAAQ,gBAC9B,oBACE,MACA,YACA,QAAQ,aACR,QAAQ,aACR,IAAI;AAGR,YAAQ,kBAAkB,QAAQ,IAA0B;EAC9D;AAEA,QAAM,YAAY,CAKhB,MACA,OACA,YACE;AACF,eAAW,YAAY,OAAO;AAC5B,UAAI,CAAC,MAAM,eAAe,QAAQ,GAAG;AACnC;;AAEF,YAAM,aAAa,MAAM,QAAQ;AACjC,YAAM,YAAY,OAAO,MAAM;AAC/B,YAAM,QAAQ,IAAI,SAAS,SAAS;AAEpC,OAAC,OAAO,MAAM,IAAI,IAAI,KACpB,SAAS,UAAU,KAClB,SAAS,CAAC,MAAM,OACnB,CAAC,aAAa,UAAU,IACpB,UAAU,WAAW,YAAY,OAAO,IACxC,cAAc,WAAW,YAAY,OAAO;;EAEpD;AAEA,QAAM,WAA0C,CAC9C,MACA,OACA,UAAU,CAAA,MACR;AACF,UAAM,QAAQ,IAAI,SAAS,IAAI;AAC/B,UAAM,eAAe,OAAO,MAAM,IAAI,IAAI;AAC1C,UAAM,aAAa,YAAY,KAAK;AAEpC,QAAI,aAAa,MAAM,UAAU;AAEjC,QAAI,cAAc;AAChB,gBAAU,MAAM,KAAK;QACnB;QACA,QAAQ,YAAY,WAAW;MAChC,CAAA;AAED,WACG,gBAAgB,WACf,gBAAgB,eAChB,yBAAyB,WACzB,yBAAyB,gBAC3B,QAAQ,aACR;AACA,kBAAU,MAAM,KAAK;UACnB;UACA,aAAa,eAAe,gBAAgB,WAAW;UACvD,SAAS,UAAU,MAAM,UAAU;QACpC,CAAA;;WAEE;AACL,eAAS,CAAC,MAAM,MAAM,CAAC,kBAAkB,UAAU,IAC/C,UAAU,MAAM,YAAY,OAAO,IACnC,cAAc,MAAM,YAAY,OAAO;;AAG7C,cAAU,MAAM,MAAM,KAAK,UAAU,MAAM,KAAK,EAAE,GAAG,WAAU,CAAE;AACjE,cAAU,MAAM,KAAK;MACnB,MAAM,OAAO,QAAQ,OAAO;MAC5B,QAAQ,YAAY,WAAW;IAChC,CAAA;EACH;AAEA,QAAM,WAA0B,OAAO,UAAS;AAC9C,WAAO,QAAQ;AACf,UAAM,SAAS,MAAM;AACrB,QAAI,OAAe,OAAO;AAC1B,QAAI,sBAAsB;AAC1B,UAAM,QAAe,IAAI,SAAS,IAAI;AACtC,UAAM,6BAA6B,CAAC,eAAuB;AACzD,4BACE,OAAO,MAAM,UAAU,KACtB,aAAa,UAAU,KAAK,MAAM,WAAW,QAAO,CAAE,KACvD,UAAU,YAAY,IAAI,aAAa,MAAM,UAAU,CAAC;IAC5D;AACA,UAAM,6BAA6B,mBAAmB,SAAS,IAAI;AACnE,UAAM,4BAA4B,mBAChC,SAAS,cAAc;AAGzB,QAAI,OAAO;AACT,UAAI;AACJ,UAAI;AACJ,YAAM,aAAa,OAAO,OACtB,cAAc,MAAM,EAAE,IACtB,cAAc,KAAK;AACvB,YAAM,cACJ,MAAM,SAAS,OAAO,QAAQ,MAAM,SAAS,OAAO;AACtD,YAAM,uBACH,CAAC,cAAc,MAAM,EAAE,KACtB,CAAC,SAAS,YACV,CAAC,IAAI,WAAW,QAAQ,IAAI,KAC5B,CAAC,MAAM,GAAG,QACZ,eACE,aACA,IAAI,WAAW,eAAe,IAAI,GAClC,WAAW,aACX,2BACA,0BAA0B;AAE9B,YAAM,UAAU,UAAU,MAAM,QAAQ,WAAW;AAEnD,UAAI,aAAa,MAAM,UAAU;AAEjC,UAAI,aAAa;AACf,cAAM,GAAG,UAAU,MAAM,GAAG,OAAO,KAAK;AACxC,8BAAsB,mBAAmB,CAAC;iBACjC,MAAM,GAAG,UAAU;AAC5B,cAAM,GAAG,SAAS,KAAK;;AAGzB,YAAM,aAAa,oBAAoB,MAAM,YAAY,WAAW;AAEpE,YAAM,eAAe,CAAC,cAAc,UAAU,KAAK;AAEnD,OAAC,eACC,UAAU,MAAM,KAAK;QACnB;QACA,MAAM,MAAM;QACZ,QAAQ,YAAY,WAAW;MAChC,CAAA;AAEH,UAAI,sBAAsB;AACxB,YAAI,gBAAgB,WAAW,yBAAyB,SAAS;AAC/D,cAAI,SAAS,SAAS,UAAU;AAC9B,gBAAI,aAAa;AACf,wBAAS;;qBAEF,CAAC,aAAa;AACvB,sBAAS;;;AAIb,eACE,gBACA,UAAU,MAAM,KAAK,EAAE,MAAM,GAAI,UAAU,CAAA,IAAK,WAAW,CAAE;;AAIjE,OAAC,eAAe,WAAW,UAAU,MAAM,KAAK,EAAE,GAAG,WAAU,CAAE;AAEjE,UAAI,SAAS,UAAU;AACrB,cAAM,EAAE,OAAM,IAAK,MAAM,WAAW,CAAC,IAAI,CAAC;AAE1C,mCAA2B,UAAU;AAErC,YAAI,qBAAqB;AACvB,gBAAM,4BAA4B,kBAChC,WAAW,QACX,SACA,IAAI;AAEN,gBAAM,oBAAoB,kBACxB,QACA,SACA,0BAA0B,QAAQ,IAAI;AAGxC,kBAAQ,kBAAkB;AAC1B,iBAAO,kBAAkB;AAEzB,oBAAU,cAAc,MAAM;;aAE3B;AACL,4BAAoB,CAAC,IAAI,GAAG,IAAI;AAChC,iBACE,MAAM,cACJ,OACA,OAAO,UACP,aACA,kCACA,SAAS,yBAAyB,GAEpC,IAAI;AACN,4BAAoB,CAAC,IAAI,CAAC;AAE1B,mCAA2B,UAAU;AAErC,YAAI,qBAAqB;AACvB,cAAI,OAAO;AACT,sBAAU;qBAEV,gBAAgB,WAChB,yBAAyB,SACzB;AACA,sBAAU,MAAM,yBAAyB,SAAS,IAAI;;;;AAK5D,UAAI,qBAAqB;AACvB,cAAM,GAAG,QACP,QACE,MAAM,GAAG,IAEoB;AAEjC,4BAAoB,MAAM,SAAS,OAAO,UAAU;;;EAG1D;AAEA,QAAM,cAAc,CAAC,KAAU,QAAe;AAC5C,QAAI,IAAI,WAAW,QAAQ,GAAG,KAAK,IAAI,OAAO;AAC5C,UAAI,MAAK;AACT,aAAO;;AAET;EACF;AAEA,QAAM,UAAwC,OAAO,MAAM,UAAU,CAAA,MAAM;AACzE,QAAI;AACJ,QAAI;AACJ,UAAM,aAAa,sBAAsB,IAAI;AAE7C,QAAI,SAAS,UAAU;AACrB,YAAM,SAAS,MAAM,4BACnB,YAAY,IAAI,IAAI,OAAO,UAAU;AAGvC,gBAAU,cAAc,MAAM;AAC9B,yBAAmB,OACf,CAAC,WAAW,KAAK,CAACH,UAAS,IAAI,QAAQA,KAAI,CAAC,IAC5C;eACK,MAAM;AACf,0BACE,MAAM,QAAQ,IACZ,WAAW,IAAI,OAAO,cAAa;AACjC,cAAM,QAAQ,IAAI,SAAS,SAAS;AACpC,eAAO,MAAM,yBACX,SAAS,MAAM,KAAK,EAAE,CAAC,SAAS,GAAG,MAAK,IAAK,KAAK;OAErD,CAAC,GAEJ,MAAM,OAAO;AACf,QAAE,CAAC,oBAAoB,CAAC,WAAW,YAAY,UAAS;WACnD;AACL,yBAAmB,UAAU,MAAM,yBAAyB,OAAO;;AAGrE,cAAU,MAAM,KAAK;MACnB,GAAI,CAAC,SAAS,IAAI,MAChB,gBAAgB,WAAW,yBAAyB,YACpD,YAAY,WAAW,UACrB,CAAA,IACA,EAAE,KAAI;MACV,GAAI,SAAS,YAAY,CAAC,OAAO,EAAE,QAAO,IAAK,CAAA;MAC/C,QAAQ,WAAW;IACpB,CAAA;AAED,YAAQ,eACN,CAAC,oBACD,sBACE,SACA,aACA,OAAO,aAAa,OAAO,KAAK;AAGpC,WAAO;EACT;AAEA,QAAM,YAA4C,CAChD,eAGE;AACF,UAAM,SAAS;MACb,GAAI,OAAO,QAAQ,cAAc;;AAGnC,WAAO,YAAY,UAAU,IACzB,SACA,SAAS,UAAU,IACjB,IAAI,QAAQ,UAAU,IACtB,WAAW,IAAI,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC;EAClD;AAEA,QAAM,gBAAoD,CACxD,MACA,eACI;IACJ,SAAS,CAAC,CAAC,KAAK,aAAa,YAAY,QAAQ,IAAI;IACrD,SAAS,CAAC,CAAC,KAAK,aAAa,YAAY,aAAa,IAAI;IAC1D,OAAO,KAAK,aAAa,YAAY,QAAQ,IAAI;IACjD,cAAc,CAAC,CAAC,IAAI,WAAW,kBAAkB,IAAI;IACrD,WAAW,CAAC,CAAC,KAAK,aAAa,YAAY,eAAe,IAAI;EAC/D;AAED,QAAM,cAAgD,CAAC,SAAQ;AAC7D,YACE,sBAAsB,IAAI,EAAE,QAAQ,CAAC,cACnC,MAAM,WAAW,QAAQ,SAAS,CAAC;AAGvC,cAAU,MAAM,KAAK;MACnB,QAAQ,OAAO,WAAW,SAAS,CAAA;IACpC,CAAA;EACH;AAEA,QAAM,WAA0C,CAAC,MAAM,OAAO,YAAW;AACvE,UAAM,OAAO,IAAI,SAAS,MAAM,EAAE,IAAI,CAAA,EAAE,CAAE,EAAE,MAAM,CAAA,GAAI;AACtD,UAAM,eAAe,IAAI,WAAW,QAAQ,IAAI,KAAK,CAAA;AAGrD,UAAM,EAAE,KAAK,YAAY,SAAS,MAAM,GAAG,gBAAe,IAAK;AAE/D,QAAI,WAAW,QAAQ,MAAM;MAC3B,GAAG;MACH,GAAG;MACH;IACD,CAAA;AAED,cAAU,MAAM,KAAK;MACnB;MACA,QAAQ,WAAW;MACnB,SAAS;IACV,CAAA;AAED,eAAW,QAAQ,eAAe,OAAO,IAAI,SAAS,IAAI,MAAK;EACjE;AAEA,QAAM,QAAoC,CACxC,MAIA,iBAEA,WAAW,IAAI,IACX,UAAU,MAAM,UAAU;IACxB,MAAM,CAAC,YACL,KACE,UAAU,QAAW,YAAY,GACjC,OAIC;GAEN,IACD,UACE,MACA,cACA,IAAI;AAGZ,QAAM,aAA0C,CAACI,WAC/C,UAAU,MAAM,UAAU;IACxB,MAAM,CACJ,cAIE;AACF,UACE,sBAAsBA,OAAM,MAAM,UAAU,MAAMA,OAAM,KAAK,KAC7D,sBACE,WACCA,OAAM,aAA+B,iBACtC,eACAA,OAAM,YAAY,GAEpB;AACA,QAAAA,OAAM,SAAS;UACb,QAAQ,EAAE,GAAG,YAAW;UACxB,GAAG;UACH,GAAG;QACJ,CAAA;;;GAGN,EAAE;AAEL,QAAM,YAA4C,CAACA,WAAS;AAC1D,WAAO,QAAQ;AACf,+BAA2B;MACzB,GAAG;MACH,GAAGA,OAAM;;AAEX,WAAO,WAAW;MAChB,GAAGA;MACH,WAAW;IACZ,CAAA;EACH;AAEA,QAAM,aAA8C,CAAC,MAAM,UAAU,CAAA,MAAM;AACzE,eAAW,aAAa,OAAO,sBAAsB,IAAI,IAAI,OAAO,OAAO;AACzE,aAAO,MAAM,OAAO,SAAS;AAC7B,aAAO,MAAM,OAAO,SAAS;AAE7B,UAAI,CAAC,QAAQ,WAAW;AACtB,cAAM,SAAS,SAAS;AACxB,cAAM,aAAa,SAAS;;AAG9B,OAAC,QAAQ,aAAa,MAAM,WAAW,QAAQ,SAAS;AACxD,OAAC,QAAQ,aAAa,MAAM,WAAW,aAAa,SAAS;AAC7D,OAAC,QAAQ,eAAe,MAAM,WAAW,eAAe,SAAS;AACjE,OAAC,QAAQ,oBACP,MAAM,WAAW,kBAAkB,SAAS;AAC9C,OAAC,SAAS,oBACR,CAAC,QAAQ,oBACT,MAAM,gBAAgB,SAAS;;AAGnC,cAAU,MAAM,KAAK;MACnB,QAAQ,YAAY,WAAW;IAChC,CAAA;AAED,cAAU,MAAM,KAAK;MACnB,GAAG;MACH,GAAI,CAAC,QAAQ,YAAY,CAAA,IAAK,EAAE,SAAS,UAAS,EAAE;IACrD,CAAA;AAED,KAAC,QAAQ,eAAe,UAAS;EACnC;AAEA,QAAM,oBAAgE,CAAC,EACrE,UACA,KAAI,MACD;AACH,QACG,UAAU,QAAQ,KAAK,OAAO,SAC/B,CAAC,CAAC,YACF,OAAO,SAAS,IAAI,IAAI,GACxB;AACA,iBAAW,OAAO,SAAS,IAAI,IAAI,IAAI,OAAO,SAAS,OAAO,IAAI;;EAEtE;AAEA,QAAM,WAA0C,CAAC,MAAM,UAAU,CAAA,MAAM;AACrE,QAAI,QAAQ,IAAI,SAAS,IAAI;AAC7B,UAAM,oBACJ,UAAU,QAAQ,QAAQ,KAAK,UAAU,SAAS,QAAQ;AAE5D,QAAI,SAAS,MAAM;MACjB,GAAI,SAAS,CAAA;MACb,IAAI;QACF,GAAI,SAAS,MAAM,KAAK,MAAM,KAAK,EAAE,KAAK,EAAE,KAAI,EAAE;QAClD;QACA,OAAO;QACP,GAAG;MACJ;IACF,CAAA;AACD,WAAO,MAAM,IAAI,IAAI;AAErB,QAAI,OAAO;AACT,wBAAkB;QAChB,UAAU,UAAU,QAAQ,QAAQ,IAChC,QAAQ,WACR,SAAS;QACb;MACD,CAAA;WACI;AACL,0BAAoB,MAAM,MAAM,QAAQ,KAAK;;AAG/C,WAAO;MACL,GAAI,oBACA,EAAE,UAAU,QAAQ,YAAY,SAAS,SAAQ,IACjD,CAAA;MACJ,GAAI,SAAS,cACT;QACE,UAAU,CAAC,CAAC,QAAQ;QACpB,KAAK,aAAa,QAAQ,GAAG;QAC7B,KAAK,aAAa,QAAQ,GAAG;QAC7B,WAAW,aAAqB,QAAQ,SAAS;QACjD,WAAW,aAAa,QAAQ,SAAS;QACzC,SAAS,aAAa,QAAQ,OAAO;MACtC,IACD,CAAA;MACJ;MACA;MACA,QAAQ;MACR,KAAK,CAAC,QAAsC;AAC1C,YAAI,KAAK;AACP,mBAAS,MAAM,OAAO;AACtB,kBAAQ,IAAI,SAAS,IAAI;AAEzB,gBAAM,WAAW,YAAY,IAAI,KAAK,IAClC,IAAI,mBACD,IAAI,iBAAiB,uBAAuB,EAAE,CAAC,KAAa,MAC7D,MACF;AACJ,gBAAM,kBAAkB,kBAAkB,QAAQ;AAClD,gBAAM,OAAO,MAAM,GAAG,QAAQ,CAAA;AAE9B,cACE,kBACI,KAAK,KAAK,CAAC,WAAgB,WAAW,QAAQ,IAC9C,aAAa,MAAM,GAAG,KAC1B;AACA;;AAGF,cAAI,SAAS,MAAM;YACjB,IAAI;cACF,GAAG,MAAM;cACT,GAAI,kBACA;gBACE,MAAM;kBACJ,GAAG,KAAK,OAAO,IAAI;kBACnB;kBACA,GAAI,MAAM,QAAQ,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,IAAI,CAAA;gBACvD;gBACD,KAAK,EAAE,MAAM,SAAS,MAAM,KAAI;cACjC,IACD,EAAE,KAAK,SAAQ;YACpB;UACF,CAAA;AAED,8BAAoB,MAAM,OAAO,QAAW,QAAQ;eAC/C;AACL,kBAAQ,IAAI,SAAS,MAAM,CAAA,CAAE;AAE7B,cAAI,MAAM,IAAI;AACZ,kBAAM,GAAG,QAAQ;;AAGnB,WAAC,SAAS,oBAAoB,QAAQ,qBACpC,EAAE,mBAAmB,OAAO,OAAO,IAAI,KAAK,OAAO,WACnD,OAAO,QAAQ,IAAI,IAAI;;;;EAIjC;AAEA,QAAM,cAAc,MAClB,SAAS,oBACT,sBAAsB,SAAS,aAAa,OAAO,KAAK;AAE1D,QAAM,eAAe,CAAC,aAAsB;AAC1C,QAAI,UAAU,QAAQ,GAAG;AACvB,gBAAU,MAAM,KAAK,EAAE,SAAQ,CAAE;AACjC,4BACE,SACA,CAAC,KAAK,SAAQ;AACZ,cAAM,eAAsB,IAAI,SAAS,IAAI;AAC7C,YAAI,cAAc;AAChB,cAAI,WAAW,aAAa,GAAG,YAAY;AAE3C,cAAI,MAAM,QAAQ,aAAa,GAAG,IAAI,GAAG;AACvC,yBAAa,GAAG,KAAK,QAAQ,CAAC,aAAY;AACxC,uBAAS,WAAW,aAAa,GAAG,YAAY;YAClD,CAAC;;;MAGP,GACA,GACA,KAAK;;EAGX;AAEA,QAAM,eACJ,CAAC,SAAS,cAAc,OAAO,MAAK;AAClC,QAAI,eAAe;AACnB,QAAI,GAAG;AACL,QAAE,kBAAkB,EAAE,eAAc;AACnC,QAA+B,WAC7B,EAA+B,QAAO;;AAE3C,QAAI,cACF,YAAY,WAAW;AAEzB,cAAU,MAAM,KAAK;MACnB,cAAc;IACf,CAAA;AAED,QAAI,SAAS,UAAU;AACrB,YAAM,EAAE,QAAQ,OAAM,IAAK,MAAM,WAAU;AAC3C,iBAAW,SAAS;AACpB,oBAAc;WACT;AACL,YAAM,yBAAyB,OAAO;;AAGxC,QAAI,OAAO,SAAS,MAAM;AACxB,iBAAW,QAAQ,OAAO,UAAU;AAClC,YAAI,aAAa,MAAM,MAAS;;;AAIpC,UAAM,WAAW,QAAQ,MAAM;AAE/B,QAAI,cAAc,WAAW,MAAM,GAAG;AACpC,gBAAU,MAAM,KAAK;QACnB,QAAQ,CAAA;MACT,CAAA;AACD,UAAI;AACF,cAAM,QAAQ,aAAmC,CAAC;eAC3C,OAAO;AACd,uBAAe;;WAEZ;AACL,UAAI,WAAW;AACb,cAAM,UAAU,EAAE,GAAG,WAAW,OAAM,GAAI,CAAC;;AAE7C,kBAAW;AACX,iBAAW,WAAW;;AAGxB,cAAU,MAAM,KAAK;MACnB,aAAa;MACb,cAAc;MACd,oBAAoB,cAAc,WAAW,MAAM,KAAK,CAAC;MACzD,aAAa,WAAW,cAAc;MACtC,QAAQ,WAAW;IACpB,CAAA;AACD,QAAI,cAAc;AAChB,YAAM;;EAEV;AAEF,QAAM,aAA8C,CAAC,MAAM,UAAU,CAAA,MAAM;AACzE,QAAI,IAAI,SAAS,IAAI,GAAG;AACtB,UAAI,YAAY,QAAQ,YAAY,GAAG;AACrC,iBAAS,MAAM,YAAY,IAAI,gBAAgB,IAAI,CAAC,CAAC;aAChD;AACL,iBACE,MACA,QAAQ,YAA2D;AAErE,YAAI,gBAAgB,MAAM,YAAY,QAAQ,YAAY,CAAC;;AAG7D,UAAI,CAAC,QAAQ,aAAa;AACxB,cAAM,WAAW,eAAe,IAAI;;AAGtC,UAAI,CAAC,QAAQ,WAAW;AACtB,cAAM,WAAW,aAAa,IAAI;AAClC,mBAAW,UAAU,QAAQ,eACzB,UAAU,MAAM,YAAY,IAAI,gBAAgB,IAAI,CAAC,CAAC,IACtD,UAAS;;AAGf,UAAI,CAAC,QAAQ,WAAW;AACtB,cAAM,WAAW,QAAQ,IAAI;AAC7B,wBAAgB,WAAW,UAAS;;AAGtC,gBAAU,MAAM,KAAK,EAAE,GAAG,WAAU,CAAE;;EAE1C;AAEA,QAAM,SAAqC,CACzC,YACA,mBAAmB,CAAA,MACjB;AACF,UAAM,gBAAgB,aAAa,YAAY,UAAU,IAAI;AAC7D,UAAM,qBAAqB,YAAY,aAAa;AACpD,UAAM,qBAAqB,cAAc,UAAU;AACnD,UAAM,SAAS,qBAAqB,iBAAiB;AAErD,QAAI,CAAC,iBAAiB,mBAAmB;AACvC,uBAAiB;;AAGnB,QAAI,CAAC,iBAAiB,YAAY;AAChC,UAAI,iBAAiB,iBAAiB;AACpC,cAAM,gBAAgB,oBAAI,IAAI;UAC5B,GAAG,OAAO;UACV,GAAG,OAAO,KAAK,eAAe,gBAAgB,WAAW,CAAC;QAC3D,CAAA;AACD,mBAAW,aAAa,MAAM,KAAK,aAAa,GAAG;AACjD,cAAI,WAAW,aAAa,SAAS,IACjC,IAAI,QAAQ,WAAW,IAAI,aAAa,SAAS,CAAC,IAClD,SACE,WACA,IAAI,QAAQ,SAAS,CAAC;;aAGzB;AACL,YAAI,SAAS,YAAY,UAAU,GAAG;AACpC,qBAAW,QAAQ,OAAO,OAAO;AAC/B,kBAAM,QAAQ,IAAI,SAAS,IAAI;AAC/B,gBAAI,SAAS,MAAM,IAAI;AACrB,oBAAM,iBAAiB,MAAM,QAAQ,MAAM,GAAG,IAAI,IAC9C,MAAM,GAAG,KAAK,CAAC,IACf,MAAM,GAAG;AAEb,kBAAI,cAAc,cAAc,GAAG;AACjC,sBAAM,OAAO,eAAe,QAAQ,MAAM;AAC1C,oBAAI,MAAM;AACR,uBAAK,MAAK;AACV;;;;;;AAOV,mBAAW,aAAa,OAAO,OAAO;AACpC,mBACE,WACA,IAAI,QAAQ,SAAS,CAAC;;;AAK5B,oBAAc,YAAY,MAAM;AAEhC,gBAAU,MAAM,KAAK;QACnB,QAAQ,EAAE,GAAG,OAAM;MACpB,CAAA;AAED,gBAAU,MAAM,KAAK;QACnB,QAAQ,EAAE,GAAG,OAAM;MACpB,CAAA;;AAGH,aAAS;MACP,OAAO,iBAAiB,kBAAkB,OAAO,QAAQ,oBAAI,IAAG;MAChE,SAAS,oBAAI,IAAG;MAChB,OAAO,oBAAI,IAAG;MACd,UAAU,oBAAI,IAAG;MACjB,OAAO,oBAAI,IAAG;MACd,UAAU;MACV,OAAO;;AAGT,WAAO,QACL,CAAC,gBAAgB,WACjB,CAAC,CAAC,iBAAiB,eACnB,CAAC,CAAC,iBAAiB;AAErB,WAAO,QAAQ,CAAC,CAAC,SAAS;AAE1B,cAAU,MAAM,KAAK;MACnB,aAAa,iBAAiB,kBAC1B,WAAW,cACX;MACJ,SAAS,qBACL,QACA,iBAAiB,YACf,WAAW,UACX,CAAC,EACC,iBAAiB,qBACjB,CAAC,UAAU,YAAY,cAAc;MAE7C,aAAa,iBAAiB,kBAC1B,WAAW,cACX;MACJ,aAAa,qBACT,CAAA,IACA,iBAAiB,kBACf,iBAAiB,qBAAqB,cACpC,eAAe,gBAAgB,WAAW,IAC1C,WAAW,cACb,iBAAiB,qBAAqB,aACpC,eAAe,gBAAgB,UAAU,IACzC,iBAAiB,YACf,WAAW,cACX,CAAA;MACV,eAAe,iBAAiB,cAC5B,WAAW,gBACX,CAAA;MACJ,QAAQ,iBAAiB,aAAa,WAAW,SAAS,CAAA;MAC1D,oBAAoB,iBAAiB,yBACjC,WAAW,qBACX;MACJ,cAAc;IACf,CAAA;EACH;AAEA,QAAM,QAAoC,CAAC,YAAY,qBACrD,OACE,WAAW,UAAU,IAChB,WAAwB,WAA2B,IACpD,YACJ,gBAAgB;AAGpB,QAAM,WAA0C,CAAC,MAAM,UAAU,CAAA,MAAM;AACrE,UAAM,QAAQ,IAAI,SAAS,IAAI;AAC/B,UAAM,iBAAiB,SAAS,MAAM;AAEtC,QAAI,gBAAgB;AAClB,YAAM,WAAW,eAAe,OAC5B,eAAe,KAAK,CAAC,IACrB,eAAe;AAEnB,UAAI,SAAS,OAAO;AAClB,iBAAS,MAAK;AACd,gBAAQ,gBACN,WAAW,SAAS,MAAM,KAC1B,SAAS,OAAM;;;EAGvB;AAEA,QAAM,gBAAgB,CACpB,qBACE;AACF,iBAAa;MACX,GAAG;MACH,GAAG;;EAEP;AAEA,QAAM,sBAAsB,MAC1B,WAAW,SAAS,aAAa,KAChC,SAAS,cAA0B,EAAG,KAAK,CAAC,WAAwB;AACnE,UAAM,QAAQ,SAAS,YAAY;AACnC,cAAU,MAAM,KAAK;MACnB,WAAW;IACZ,CAAA;EACH,CAAC;AAEH,QAAM,UAAU;IACd,SAAS;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,UAAO;AACT,eAAO;;MAET,IAAI,cAAW;AACb,eAAO;;MAET,IAAI,SAAM;AACR,eAAO;;MAET,IAAI,OAAO,OAAK;AACd,iBAAS;;MAEX,IAAI,iBAAc;AAChB,eAAO;;MAET,IAAI,SAAM;AACR,eAAO;;MAET,IAAI,OAAO,OAAK;AACd,iBAAS;;MAEX,IAAI,aAAU;AACZ,eAAO;;MAET,IAAI,WAAQ;AACV,eAAO;;MAET,IAAI,SAAS,OAAK;AAChB,mBAAW;UACT,GAAG;UACH,GAAG;;;IAGR;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAGF,SAAO;IACL,GAAG;IACH,aAAa;;AAEjB;AC9gDA,IAAA,aAAe,MAAK;AAClB,QAAM,IACJ,OAAO,gBAAgB,cAAc,KAAK,IAAG,IAAK,YAAY,IAAG,IAAK;AAExE,SAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAK;AACnE,UAAM,KAAK,KAAK,OAAM,IAAK,KAAK,KAAK,KAAK;AAE1C,YAAQ,KAAK,MAAM,IAAK,IAAI,IAAO,GAAK,SAAS,EAAE;EACrD,CAAC;AACH;ACNA,IAAA,oBAAe,CACb,MACA,OACA,UAAiC,CAAA,MAEjC,QAAQ,eAAe,YAAY,QAAQ,WAAW,IAClD,QAAQ,aACR,GAAG,IAAI,IAAI,YAAY,QAAQ,UAAU,IAAI,QAAQ,QAAQ,UAAU,MACvE;ACTN,IAAA,WAAe,CAAI,MAAW,UAAwB;EACpD,GAAG;EACH,GAAG,sBAAsB,KAAK;;ACJhC,IAAA,iBAAe,CAAI,UACjB,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,MAAM,MAAS,IAAI;ACO9B,SAAA,OACtB,MACA,OACA,OAAe;AAEf,SAAO;IACL,GAAG,KAAK,MAAM,GAAG,KAAK;IACtB,GAAG,sBAAsB,KAAK;IAC9B,GAAG,KAAK,MAAM,KAAK;;AAEvB;AChBA,IAAA,cAAe,CACb,MACA,MACA,OACqB;AACrB,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,WAAO,CAAA;;AAGT,MAAI,YAAY,KAAK,EAAE,CAAC,GAAG;AACzB,SAAK,EAAE,IAAI;;AAEb,OAAK,OAAO,IAAI,GAAG,KAAK,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC;AAE1C,SAAO;AACT;ACfA,IAAA,YAAe,CAAI,MAAW,UAAwB;EACpD,GAAG,sBAAsB,KAAK;EAC9B,GAAG,sBAAsB,IAAI;;ACA/B,SAAS,gBAAmB,MAAW,SAAiB;AACtD,MAAI,IAAI;AACR,QAAM,OAAO,CAAC,GAAG,IAAI;AAErB,aAAW,SAAS,SAAS;AAC3B,SAAK,OAAO,QAAQ,GAAG,CAAC;AACxB;;AAGF,SAAO,QAAQ,IAAI,EAAE,SAAS,OAAO,CAAA;AACvC;AAEA,IAAA,gBAAe,CAAI,MAAW,UAC5B,YAAY,KAAK,IACb,CAAA,IACA,gBACE,MACC,sBAAsB,KAAK,EAAe,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC;ACrBxE,IAAA,cAAe,CAAI,MAAW,QAAgB,WAAwB;AACpE,GAAC,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC;AAC5D;ACFA,IAAA,WAAe,CAAI,aAAkB,OAAe,UAAY;AAC9D,cAAY,KAAK,IAAI;AACrB,SAAO;AACT;AC4EM,SAAU,cAOd,OAKC;AAED,QAAM,UAAU,eAAc;AAC9B,QAAM,EACJ,UAAU,QAAQ,SAClB,MACA,UAAU,MACV,kBACA,MAAK,IACH;AACJ,QAAM,CAAC,QAAQ,SAAS,IAAIP,aAAAA,QAAM,SAAS,QAAQ,eAAe,IAAI,CAAC;AACvE,QAAM,MAAMA,aAAAA,QAAM,OAChB,QAAQ,eAAe,IAAI,EAAE,IAAI,UAAU,CAAC;AAE9C,QAAM,YAAYA,aAAAA,QAAM,OAAO,MAAM;AACrC,QAAM,QAAQA,aAAAA,QAAM,OAAO,IAAI;AAC/B,QAAM,YAAYA,aAAAA,QAAM,OAAO,KAAK;AAEpC,QAAM,UAAU;AAChB,YAAU,UAAU;AACpB,UAAQ,OAAO,MAAM,IAAI,IAAI;AAE7B,WACG,QAA2D,SAC1D,MACA,KAAsC;AAG1CA,eAAAA,QAAM,UACJ,MACE,QAAQ,UAAU,MAAM,UAAU;IAChC,MAAM,CAAC,EACL,QACA,MAAM,eAAc,MAIjB;AACH,UAAI,mBAAmB,MAAM,WAAW,CAAC,gBAAgB;AACvD,cAAM,cAAc,IAAI,QAAQ,MAAM,OAAO;AAC7C,YAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,oBAAU,WAAW;AACrB,cAAI,UAAU,YAAY,IAAI,UAAU;;;;EAI/C,CAAA,EAAE,aACL,CAAC,OAAO,CAAC;AAGX,QAAM,eAAeA,aAAAA,QAAM,YACzB,CAKE,4BACE;AACF,cAAU,UAAU;AACpB,YAAQ,eAAe,MAAM,uBAAuB;EACtD,GACA,CAAC,SAAS,IAAI,CAAC;AAGjB,QAAM,SAAS,CACb,OAGA,YACE;AACF,UAAM,cAAc,sBAAsB,YAAY,KAAK,CAAC;AAC5D,UAAM,0BAA0B,SAC9B,QAAQ,eAAe,IAAI,GAC3B,WAAW;AAEb,YAAQ,OAAO,QAAQ,kBACrB,MACA,wBAAwB,SAAS,GACjC,OAAO;AAET,QAAI,UAAU,SAAS,IAAI,SAAS,YAAY,IAAI,UAAU,CAAC;AAC/D,iBAAa,uBAAuB;AACpC,cAAU,uBAAuB;AACjC,YAAQ,eAAe,MAAM,yBAAyB,UAAU;MAC9D,MAAM,eAAe,KAAK;IAC3B,CAAA;EACH;AAEA,QAAM,UAAU,CACd,OAGA,YACE;AACF,UAAM,eAAe,sBAAsB,YAAY,KAAK,CAAC;AAC7D,UAAM,0BAA0B,UAC9B,QAAQ,eAAe,IAAI,GAC3B,YAAY;AAEd,YAAQ,OAAO,QAAQ,kBAAkB,MAAM,GAAG,OAAO;AACzD,QAAI,UAAU,UAAU,IAAI,SAAS,aAAa,IAAI,UAAU,CAAC;AACjE,iBAAa,uBAAuB;AACpC,cAAU,uBAAuB;AACjC,YAAQ,eAAe,MAAM,yBAAyB,WAAW;MAC/D,MAAM,eAAe,KAAK;IAC3B,CAAA;EACH;AAEA,QAAM,SAAS,CAAC,UAA6B;AAC3C,UAAM,0BAEA,cAAc,QAAQ,eAAe,IAAI,GAAG,KAAK;AACvD,QAAI,UAAU,cAAc,IAAI,SAAS,KAAK;AAC9C,iBAAa,uBAAuB;AACpC,cAAU,uBAAuB;AACjC,KAAC,MAAM,QAAQ,IAAI,QAAQ,SAAS,IAAI,CAAC,KACvC,IAAI,QAAQ,SAAS,MAAM,MAAS;AACtC,YAAQ,eAAe,MAAM,yBAAyB,eAAe;MACnE,MAAM;IACP,CAAA;EACH;AAEA,QAAMQ,WAAS,CACb,OACA,OAGA,YACE;AACF,UAAM,cAAc,sBAAsB,YAAY,KAAK,CAAC;AAC5D,UAAM,0BAA0BC,OAC9B,QAAQ,eAAe,IAAI,GAC3B,OACA,WAAW;AAEb,YAAQ,OAAO,QAAQ,kBAAkB,MAAM,OAAO,OAAO;AAC7D,QAAI,UAAUA,OAAS,IAAI,SAAS,OAAO,YAAY,IAAI,UAAU,CAAC;AACtE,iBAAa,uBAAuB;AACpC,cAAU,uBAAuB;AACjC,YAAQ,eAAe,MAAM,yBAAyBA,QAAU;MAC9D,MAAM;MACN,MAAM,eAAe,KAAK;IAC3B,CAAA;EACH;AAEA,QAAM,OAAO,CAAC,QAAgB,WAAkB;AAC9C,UAAM,0BAA0B,QAAQ,eAAe,IAAI;AAC3D,gBAAY,yBAAyB,QAAQ,MAAM;AACnD,gBAAY,IAAI,SAAS,QAAQ,MAAM;AACvC,iBAAa,uBAAuB;AACpC,cAAU,uBAAuB;AACjC,YAAQ,eACN,MACA,yBACA,aACA;MACE,MAAM;MACN,MAAM;OAER,KAAK;EAET;AAEA,QAAM,OAAO,CAAC,MAAc,OAAc;AACxC,UAAM,0BAA0B,QAAQ,eAAe,IAAI;AAC3D,gBAAY,yBAAyB,MAAM,EAAE;AAC7C,gBAAY,IAAI,SAAS,MAAM,EAAE;AACjC,iBAAa,uBAAuB;AACpC,cAAU,uBAAuB;AACjC,YAAQ,eACN,MACA,yBACA,aACA;MACE,MAAM;MACN,MAAM;OAER,KAAK;EAET;AAEA,QAAM,SAAS,CACb,OACA,UACE;AACF,UAAM,cAAc,YAAY,KAAK;AACrC,UAAM,0BAA0B,SAC9B,QAAQ,eAEN,IAAI,GACN,OACA,WAAwE;AAE1E,QAAI,UAAU,CAAC,GAAG,uBAAuB,EAAE,IAAI,CAAC,MAAM,MACpD,CAAC,QAAQ,MAAM,QAAQ,WAAU,IAAK,IAAI,QAAQ,CAAC,CAAC;AAEtD,iBAAa,uBAAuB;AACpC,cAAU,CAAC,GAAG,uBAAuB,CAAC;AACtC,YAAQ,eACN,MACA,yBACA,UACA;MACE,MAAM;MACN,MAAM;IACP,GACD,MACA,KAAK;EAET;AAEA,QAAM,UAAU,CACd,UAGE;AACF,UAAM,0BAA0B,sBAAsB,YAAY,KAAK,CAAC;AACxE,QAAI,UAAU,wBAAwB,IAAI,UAAU;AACpD,iBAAa,CAAC,GAAG,uBAAuB,CAAC;AACzC,cAAU,CAAC,GAAG,uBAAuB,CAAC;AACtC,YAAQ,eACN,MACA,CAAC,GAAG,uBAAuB,GAC3B,CAAI,SAAe,MACnB,CAAA,GACA,MACA,KAAK;EAET;AAEAT,eAAAA,QAAM,UAAU,MAAK;AACnB,YAAQ,OAAO,SAAS;AAExB,cAAU,MAAM,QAAQ,MAAM,KAC5B,QAAQ,UAAU,MAAM,KAAK;MAC3B,GAAG,QAAQ;IACe,CAAA;AAE9B,QACE,UAAU,YACT,CAAC,mBAAmB,QAAQ,SAAS,IAAI,EAAE,cAC1C,QAAQ,WAAW,gBACrB,CAAC,mBAAmB,QAAQ,SAAS,cAAc,EAAE,YACrD;AACA,UAAI,QAAQ,SAAS,UAAU;AAC7B,gBAAQ,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,WAAU;AACzC,gBAAM,QAAQ,IAAI,OAAO,QAAQ,IAAI;AACrC,gBAAM,gBAAgB,IAAI,QAAQ,WAAW,QAAQ,IAAI;AAEzD,cACE,gBACK,CAAC,SAAS,cAAc,QACxB,UACE,cAAc,SAAS,MAAM,QAC5B,cAAc,YAAY,MAAM,WACpC,SAAS,MAAM,MACnB;AACA,oBACI,IAAI,QAAQ,WAAW,QAAQ,MAAM,KAAK,IAC1C,MAAM,QAAQ,WAAW,QAAQ,IAAI;AACzC,oBAAQ,UAAU,MAAM,KAAK;cAC3B,QAAQ,QAAQ,WAAW;YAC5B,CAAA;;QAEL,CAAC;aACI;AACL,cAAM,QAAe,IAAI,QAAQ,SAAS,IAAI;AAC9C,YACE,SACA,MAAM,MACN,EACE,mBAAmB,QAAQ,SAAS,cAAc,EAAE,cACpD,mBAAmB,QAAQ,SAAS,IAAI,EAAE,aAE5C;AACA,wBACE,OACA,QAAQ,OAAO,UACf,QAAQ,aACR,QAAQ,SAAS,iBAAiB,gBAAgB,KAClD,QAAQ,SAAS,2BACjB,IAAI,EACJ,KACA,CAAC,UACC,CAAC,cAAc,KAAK,KACpB,QAAQ,UAAU,MAAM,KAAK;YAC3B,QAAQ,0BACN,QAAQ,WAAW,QACnB,OACA,IAAI;UAEP,CAAA,CAAC;;;;AAMZ,YAAQ,UAAU,MAAM,KAAK;MAC3B;MACA,QAAQ,YAAY,QAAQ,WAAW;IACxC,CAAA;AAED,YAAQ,OAAO,SACb,sBAAsB,QAAQ,SAAS,CAAC,KAAK,QAAe;AAC1D,UACE,QAAQ,OAAO,SACf,IAAI,WAAW,QAAQ,OAAO,KAAK,KACnC,IAAI,OACJ;AACA,YAAI,MAAK;AACT,eAAO;;AAET;IACF,CAAC;AAEH,YAAQ,OAAO,QAAQ;AAEvB,YAAQ,UAAS;AACjB,cAAU,UAAU;KACnB,CAAC,QAAQ,MAAM,OAAO,CAAC;AAE1BA,eAAAA,QAAM,UAAU,MAAK;AACnB,KAAC,IAAI,QAAQ,aAAa,IAAI,KAAK,QAAQ,eAAe,IAAI;AAE9D,WAAO,MAAK;AACV,YAAM,gBAAgB,CAACG,OAAyB,UAAkB;AAChE,cAAM,QAAe,IAAI,QAAQ,SAASA,KAAI;AAC9C,YAAI,SAAS,MAAM,IAAI;AACrB,gBAAM,GAAG,QAAQ;;MAErB;AAEA,cAAQ,SAAS,oBAAoB,mBACjC,QAAQ,WAAW,IAA+B,IAClD,cAAc,MAAM,KAAK;IAC/B;KACC,CAAC,MAAM,SAAS,SAAS,gBAAgB,CAAC;AAE7C,SAAO;IACL,MAAMH,aAAAA,QAAM,YAAY,MAAM,CAAC,cAAc,MAAM,OAAO,CAAC;IAC3D,MAAMA,aAAAA,QAAM,YAAY,MAAM,CAAC,cAAc,MAAM,OAAO,CAAC;IAC3D,SAASA,aAAAA,QAAM,YAAY,SAAS,CAAC,cAAc,MAAM,OAAO,CAAC;IACjE,QAAQA,aAAAA,QAAM,YAAY,QAAQ,CAAC,cAAc,MAAM,OAAO,CAAC;IAC/D,QAAQA,aAAAA,QAAM,YAAY,QAAQ,CAAC,cAAc,MAAM,OAAO,CAAC;IAC/D,QAAQA,aAAAA,QAAM,YAAYQ,UAAQ,CAAC,cAAc,MAAM,OAAO,CAAC;IAC/D,QAAQR,aAAAA,QAAM,YAAY,QAAQ,CAAC,cAAc,MAAM,OAAO,CAAC;IAC/D,SAASA,aAAAA,QAAM,YAAY,SAAS,CAAC,cAAc,MAAM,OAAO,CAAC;IACjE,QAAQA,aAAAA,QAAM,QACZ,MACE,OAAO,IAAI,CAAC,OAAO,WAAW;MAC5B,GAAG;MACH,CAAC,OAAO,GAAG,IAAI,QAAQ,KAAK,KAAK,WAAU;IAC5C,EAAC,GACJ,CAAC,QAAQ,OAAO,CAAC;;AAGvB;ACzZgB,SAAA,QAKd,QAAkE,CAAA,GAAE;AAEpE,QAAM,eAAeA,aAAAA,QAAM,OAEzB,MAAS;AACX,QAAM,UAAUA,aAAAA,QAAM,OAA4B,MAAS;AAC3D,QAAM,CAAC,WAAW,eAAe,IAAIA,aAAAA,QAAM,SAAkC;IAC3E,SAAS;IACT,cAAc;IACd,WAAW,WAAW,MAAM,aAAa;IACzC,aAAa;IACb,cAAc;IACd,oBAAoB;IACpB,SAAS;IACT,aAAa;IACb,aAAa,CAAA;IACb,eAAe,CAAA;IACf,kBAAkB,CAAA;IAClB,QAAQ,MAAM,UAAU,CAAA;IACxB,UAAU,MAAM,YAAY;IAC5B,SAAS;IACT,eAAe,WAAW,MAAM,aAAa,IACzC,SACA,MAAM;EACX,CAAA;AAED,MAAI,CAAC,aAAa,SAAS;AACzB,iBAAa,UAAU;MACrB,GAAI,MAAM,cAAc,MAAM,cAAc,kBAAkB,KAAK;MACnE;;AAGF,QACE,MAAM,eACN,MAAM,iBACN,CAAC,WAAW,MAAM,aAAa,GAC/B;AACA,YAAM,YAAY,MAAM,MAAM,eAAe,MAAM,YAAY;;;AAInE,QAAM,UAAU,aAAa,QAAQ;AACrC,UAAQ,WAAW;AAEnB,4BAA0B,MAAK;AAC7B,UAAM,MAAM,QAAQ,WAAW;MAC7B,WAAW,QAAQ;MACnB,UAAU,MAAM,gBAAgB,EAAE,GAAG,QAAQ,WAAU,CAAE;MACzD,cAAc;IACf,CAAA;AAED,oBAAgB,CAAC,UAAU;MACzB,GAAG;MACH,SAAS;IACV,EAAC;AAEF,YAAQ,WAAW,UAAU;AAE7B,WAAO;EACT,GAAG,CAAC,OAAO,CAAC;AAEZA,eAAAA,QAAM,UACJ,MAAM,QAAQ,aAAa,MAAM,QAAQ,GACzC,CAAC,SAAS,MAAM,QAAQ,CAAC;AAG3BA,eAAAA,QAAM,UAAU,MAAK;AACnB,QAAI,MAAM,MAAM;AACd,cAAQ,SAAS,OAAO,MAAM;;AAEhC,QAAI,MAAM,gBAAgB;AACxB,cAAQ,SAAS,iBAAiB,MAAM;;EAE5C,GAAG,CAAC,SAAS,MAAM,MAAM,MAAM,cAAc,CAAC;AAE9CA,eAAAA,QAAM,UAAU,MAAK;AACnB,QAAI,MAAM,QAAQ;AAChB,cAAQ,WAAW,MAAM,MAAM;AAC/B,cAAQ,YAAW;;KAEpB,CAAC,SAAS,MAAM,MAAM,CAAC;AAE1BA,eAAAA,QAAM,UAAU,MAAK;AACnB,UAAM,oBACJ,QAAQ,UAAU,MAAM,KAAK;MAC3B,QAAQ,QAAQ,UAAS;IAC1B,CAAA;KACF,CAAC,SAAS,MAAM,gBAAgB,CAAC;AAEpCA,eAAAA,QAAM,UAAU,MAAK;AACnB,QAAI,QAAQ,gBAAgB,SAAS;AACnC,YAAM,UAAU,QAAQ,UAAS;AACjC,UAAI,YAAY,UAAU,SAAS;AACjC,gBAAQ,UAAU,MAAM,KAAK;UAC3B;QACD,CAAA;;;KAGJ,CAAC,SAAS,UAAU,OAAO,CAAC;AAE/BA,eAAAA,QAAM,UAAU,MAAK;AACnB,QAAI,MAAM,UAAU,CAAC,UAAU,MAAM,QAAQ,QAAQ,OAAO,GAAG;AAC7D,cAAQ,OAAO,MAAM,QAAQ,QAAQ,SAAS,YAAY;AAC1D,cAAQ,UAAU,MAAM;AACxB,sBAAgB,CAAC,WAAW,EAAE,GAAG,MAAK,EAAG;WACpC;AACL,cAAQ,oBAAmB;;KAE5B,CAAC,SAAS,MAAM,MAAM,CAAC;AAE1BA,eAAAA,QAAM,UAAU,MAAK;AACnB,QAAI,CAAC,QAAQ,OAAO,OAAO;AACzB,cAAQ,UAAS;AACjB,cAAQ,OAAO,QAAQ;;AAGzB,QAAI,QAAQ,OAAO,OAAO;AACxB,cAAQ,OAAO,QAAQ;AACvB,cAAQ,UAAU,MAAM,KAAK,EAAE,GAAG,QAAQ,WAAU,CAAE;;AAGxD,YAAQ,iBAAgB;EAC1B,CAAC;AAED,eAAa,QAAQ,YAAY,kBAAkB,WAAW,OAAO;AAErE,SAAO,aAAa;AACtB;", "names": ["result", "React", "formState", "field", "name", "value", "isCheckBox", "isRadioOrCheckbox", "props", "insert", "insertAt"]}