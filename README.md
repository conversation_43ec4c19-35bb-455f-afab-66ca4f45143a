# ScensPack - Test Scenario Search Application

A comprehensive web application for managing and searching test scenarios with AI-powered generation using OpenAI.

## Features

### 🔐 User Authentication
- Sign up and sign in with email and password
- Role-based access (Developer/Tester)
- JWT-based authentication
- Protected routes

### 🔍 Test Scenario Search
- Search scenarios by feature, type, or keyword
- Advanced filtering options
- Role-specific views (automation/manual for testers)
- Real-time search results

### 🤖 AI-Powered Scenario Generation
- Generate test scenarios using OpenAI GPT
- Customizable by feature, type, and testing method
- Automatic scenario categorization
- Export generated scenarios

### 👥 Role-Based Features
- **Developers**: Focus on feature testing and automation
- **Testers**: Detailed view of manual vs automation scenarios
- Personalized dashboards and tips

## Technology Stack

### Backend
- **Node.js** with Express and TypeScript
- **Prisma** ORM with SQLite database
- **JWT** for authentication
- **OpenAI API** integration
- **bcryptjs** for password hashing

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development
- **Tailwind CSS** for styling
- **React Router** for navigation
- **React Hook Form** for form handling
- **React Query** for data fetching
- **Lucide React** for icons

## Quick Start

### Prerequisites
- Node.js 18+ and npm
- OpenAI API key (optional for AI features)

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd scenspack
npm run install:all
```

2. **Setup environment variables:**
```bash
# Backend
cd backend
cp .env.example .env
# Edit .env with your configuration
```

3. **Initialize database:**
```bash
cd backend
npm run db:push
npm run db:generate
```

4. **Start development servers:**
```bash
# From root directory
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## Environment Variables

### Backend (.env)
```env
DATABASE_URL="file:./dev.db"
JWT_SECRET="your-super-secret-jwt-key"
PORT=3001
OPENAI_API_KEY="your-openai-api-key"
OPENAI_API_URL="https://api.openai.com/v1"
```

## API Endpoints

### Authentication
- `POST /api/auth/signup` - Create new account
- `POST /api/auth/signin` - Sign in
- `GET /api/auth/me` - Get current user

### Test Scenarios
- `GET /api/scenarios` - Search scenarios with filters
- `GET /api/scenarios/:id` - Get specific scenario
- `POST /api/scenarios` - Create new scenario
- `POST /api/scenarios/generate` - Generate scenarios with AI

## Database Schema

### Users
- id, email, password, role, timestamps

### Test Scenarios
- id, title, description, feature, type, isAutomation
- steps (JSON), expectedResult, tags (JSON)
- createdBy, timestamps

## Development

### Available Scripts

```bash
# Root level
npm run dev              # Start both frontend and backend
npm run build           # Build both applications
npm run install:all     # Install all dependencies

# Backend
npm run dev             # Start backend development server
npm run build           # Build backend
npm run db:push         # Push schema to database
npm run db:generate     # Generate Prisma client
npm run db:studio       # Open Prisma Studio

# Frontend
npm run dev             # Start frontend development server
npm run build           # Build frontend for production
npm run preview         # Preview production build
```

### Project Structure
```
scenspack/
├── backend/
│   ├── src/
│   │   ├── routes/          # API routes
│   │   ├── middleware/      # Auth middleware
│   │   ├── services/        # Business logic
│   │   ├── utils/           # Utilities
│   │   └── types/           # TypeScript types
│   ├── prisma/             # Database schema
│   └── package.json
├── frontend/
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/           # Page components
│   │   ├── contexts/        # React contexts
│   │   ├── services/        # API services
│   │   └── types/           # TypeScript types
│   └── package.json
└── package.json
```

## Usage

### For Developers
1. Sign up with "Developer" role
2. Search existing scenarios for your features
3. Generate new scenarios using AI
4. Focus on automation-suitable scenarios

### For Testers
1. Sign up with "Tester" role
2. Search scenarios with manual/automation filters
3. Review scenario details and test steps
4. Generate comprehensive test scenarios

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details