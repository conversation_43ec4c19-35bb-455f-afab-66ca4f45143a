import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../utils/database';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { DeepseekService } from '../services/deepseek';
import { SearchQuery, TestScenario } from '../types';

const router = express.Router();
const deepseekService = new DeepseekService();

// Get all scenarios with search and filtering
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const { feature, type, isAutomation, tags, query: searchQuery } = req.query as any;

    const where: any = {};

    if (feature) {
      where.feature = {
        contains: feature,
        mode: 'insensitive'
      };
    }

    if (type) {
      where.type = type;
    }

    if (isAutomation !== undefined) {
      where.isAutomation = isAutomation === 'true';
    }

    if (searchQuery) {
      where.OR = [
        { title: { contains: searchQuery, mode: 'insensitive' } },
        { description: { contains: searchQuery, mode: 'insensitive' } },
        { feature: { contains: searchQuery, mode: 'insensitive' } }
      ];
    }

    const scenarios = await prisma.testScenario.findMany({
      where,
      include: {
        creator: {
          select: {
            id: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Parse JSON fields
    const formattedScenarios = scenarios.map(scenario => ({
      ...scenario,
      steps: scenario.steps ? JSON.parse(scenario.steps) : [],
      tags: scenario.tags ? JSON.parse(scenario.tags) : []
    }));

    res.json({ scenarios: formattedScenarios });
  } catch (error) {
    console.error('Get scenarios error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get scenario by ID
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;

    const scenario = await prisma.testScenario.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            email: true,
            role: true
          }
        }
      }
    });

    if (!scenario) {
      return res.status(404).json({ error: 'Scenario not found' });
    }

    // Parse JSON fields
    const formattedScenario = {
      ...scenario,
      steps: scenario.steps ? JSON.parse(scenario.steps) : [],
      tags: scenario.tags ? JSON.parse(scenario.tags) : []
    };

    res.json({ scenario: formattedScenario });
  } catch (error) {
    console.error('Get scenario error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new scenario
router.post('/', 
  authenticateToken,
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('description').notEmpty().withMessage('Description is required'),
    body('feature').notEmpty().withMessage('Feature is required'),
    body('type').isIn(['FUNCTIONAL', 'INTEGRATION', 'UNIT', 'E2E', 'PERFORMANCE', 'SECURITY', 'USABILITY']),
    body('isAutomation').isBoolean()
  ],
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { title, description, feature, type, isAutomation, steps, expectedResult, tags } = req.body;

      const scenario = await prisma.testScenario.create({
        data: {
          title,
          description,
          feature,
          type,
          isAutomation,
          steps: steps ? JSON.stringify(steps) : null,
          expectedResult,
          tags: tags ? JSON.stringify(tags) : null,
          createdBy: req.user!.id
        },
        include: {
          creator: {
            select: {
              id: true,
              email: true,
              role: true
            }
          }
        }
      });

      // Parse JSON fields for response
      const formattedScenario = {
        ...scenario,
        steps: scenario.steps ? JSON.parse(scenario.steps) : [],
        tags: scenario.tags ? JSON.parse(scenario.tags) : []
      };

      res.status(201).json({ scenario: formattedScenario });
    } catch (error) {
      console.error('Create scenario error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Generate scenarios using Deepseek
router.post('/generate', 
  authenticateToken,
  [
    body('feature').notEmpty().withMessage('Feature is required'),
    body('type').isIn(['FUNCTIONAL', 'INTEGRATION', 'UNIT', 'E2E', 'PERFORMANCE', 'SECURITY', 'USABILITY']),
    body('isAutomation').isBoolean()
  ],
  async (req: AuthenticatedRequest, res: express.Response) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { feature, type, isAutomation, additionalContext } = req.body;

      // Generate scenarios using Deepseek
      const deepseekResponse = await deepseekService.generateScenarios({
        feature,
        type,
        isAutomation,
        additionalContext
      });

      // Save generated scenarios to database
      const savedScenarios = [];
      for (const scenarioData of deepseekResponse.scenarios) {
        const scenario = await prisma.testScenario.create({
          data: {
            title: scenarioData.title,
            description: scenarioData.description,
            feature,
            type,
            isAutomation,
            steps: JSON.stringify(scenarioData.steps),
            expectedResult: scenarioData.expectedResult,
            tags: JSON.stringify(scenarioData.tags),
            createdBy: req.user!.id
          },
          include: {
            creator: {
              select: {
                id: true,
                email: true,
                role: true
              }
            }
          }
        });

        savedScenarios.push({
          ...scenario,
          steps: JSON.parse(scenario.steps || '[]'),
          tags: JSON.parse(scenario.tags || '[]')
        });
      }

      res.status(201).json({ scenarios: savedScenarios });
    } catch (error) {
      console.error('Generate scenarios error:', error);
      res.status(500).json({ error: 'Failed to generate scenarios' });
    }
  }
);

export default router;
