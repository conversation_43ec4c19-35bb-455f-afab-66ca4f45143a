import axios from 'axios';
import { DeepseekRequest, DeepseekResponse } from '../types';

const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const DEEPSEEK_API_URL = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/v1';

export class DeepseekService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = DEEPSEEK_API_KEY || '';
    this.baseUrl = DEEPSEEK_API_URL;
  }

  async generateScenarios(req: DeepseekRequest): Promise<DeepseekResponse> {
    if (!this.apiKey) {
      throw new Error('Deepseek API key not configured');
    }

    const prompt = this.buildPrompt(req);

    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: 'You are a QA expert that generates comprehensive test scenarios. Always respond with valid JSON format.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 2000
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const content = response.data.choices[0].message.content;
      return this.parseResponse(content, req);
    } catch (error) {
      console.error('Deepseek API error:', error);
      throw new Error('Failed to generate scenarios from Deepseek');
    }
  }

  private buildPrompt(req: DeepseekRequest): string {
    const automationType = req.isAutomation ? 'automation' : 'manual';
    
    return `Generate 3-5 comprehensive test scenarios for the following:

Feature: ${req.feature}
Test Type: ${req.type}
Testing Method: ${automationType} testing
${req.additionalContext ? `Additional Context: ${req.additionalContext}` : ''}

Please provide test scenarios in the following JSON format:
{
  "scenarios": [
    {
      "title": "Clear, descriptive test scenario title",
      "description": "Detailed description of what this test validates",
      "steps": ["Step 1", "Step 2", "Step 3"],
      "expectedResult": "What should happen when the test passes",
      "tags": ["tag1", "tag2", "tag3"]
    }
  ]
}

Focus on:
- Edge cases and boundary conditions
- User experience scenarios
- Error handling
- ${req.isAutomation ? 'Scenarios suitable for automation with clear assertions' : 'Scenarios that require human judgment and observation'}
- Real-world usage patterns`;
  }

  private parseResponse(content: string, req: DeepseekRequest): DeepseekResponse {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback: try to parse the entire content
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse Deepseek response:', content);
      // Return a fallback response
      return {
        scenarios: [
          {
            title: `Test ${req.feature} functionality`,
            description: `Verify that ${req.feature} works as expected`,
            steps: [
              'Navigate to the feature',
              'Perform the main action',
              'Verify the result'
            ],
            expectedResult: 'Feature should work correctly',
            tags: [req.feature.toLowerCase(), req.type.toLowerCase()]
          }
        ]
      };
    }
  }
}
