export interface User {
  id: string;
  email: string;
  role: string; // 'DEVELOPER' | 'TESTER'
  createdAt: Date;
}

export interface TestScenario {
  id: string;
  title: string;
  description: string;
  feature: string;
  type: string; // 'FUNCTIONAL' | 'INTEGRATION' | 'UNIT' | 'E2E' | 'PERFORMANCE' | 'SECURITY' | 'USABILITY'
  isAutomation: boolean;
  steps?: string[];
  expectedResult?: string;
  tags?: string[];
  createdBy: string;
  createdAt: Date;
  creator?: User;
}

export interface AuthRequest {
  email: string;
  password: string;
  role?: string; // 'DEVELOPER' | 'TESTER'
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface SearchQuery {
  feature?: string;
  type?: string;
  isAutomation?: boolean;
  tags?: string[];
  query?: string;
}

export interface OpenAIRequest {
  feature: string;
  type: string;
  isAutomation: boolean;
  additionalContext?: string;
}

export interface OpenAIResponse {
  scenarios: {
    title: string;
    description: string;
    steps: string[];
    expectedResult: string;
    tags: string[];
  }[];
}
